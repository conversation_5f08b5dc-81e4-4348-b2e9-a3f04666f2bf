import React, { useState } from 'react';
import { apiClient, API_ENDPOINTS } from '../../config/api';
import Button from '../ui/Button';
import { useAuth } from '../../context/AuthContext';

const AdminApiTest: React.FC = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testAdminDashboard = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('Testing admin dashboard API...');
      console.log('Current user:', user);
      console.log('Auth token:', localStorage.getItem('auth_token'));
      
      const response = await apiClient.get(API_ENDPOINTS.ADMIN.DASHBOARD);
      console.log('Admin dashboard response:', response);
      setResult(response.data);
    } catch (err: any) {
      console.error('Admin dashboard error:', err);
      console.error('Error response:', err.response?.data);
      console.error('Error status:', err.response?.status);
      setError(`${err.response?.status || 'Unknown'}: ${err.response?.data?.message || err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testAdminUsers = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('Testing admin users API...');
      const response = await apiClient.get(API_ENDPOINTS.ADMIN.USERS);
      console.log('Admin users response:', response);
      setResult(response.data);
    } catch (err: any) {
      console.error('Admin users error:', err);
      setError(`${err.response?.status || 'Unknown'}: ${err.response?.data?.message || err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 border border-gray-300 rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">Admin API Test</h3>
      
      <div className="mb-4 text-sm">
        <p><strong>Current User:</strong> {user?.name || 'Not logged in'}</p>
        <p><strong>Role:</strong> {user?.role || 'No role'}</p>
        <p><strong>Has Token:</strong> {localStorage.getItem('auth_token') ? 'Yes' : 'No'}</p>
      </div>

      <div className="flex gap-2 mb-4">
        <Button 
          onClick={testAdminDashboard} 
          disabled={loading}
          size="sm"
        >
          {loading ? 'Testing...' : 'Test Dashboard'}
        </Button>

        <Button 
          onClick={testAdminUsers} 
          disabled={loading}
          size="sm"
          variant="outline"
        >
          {loading ? 'Testing...' : 'Test Users'}
        </Button>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-300 rounded text-red-700">
          <strong>Error:</strong> {error}
        </div>
      )}

      {result && (
        <div className="mb-4 p-3 bg-green-100 border border-green-300 rounded text-green-700">
          <strong>Success:</strong> API call successful
          <pre className="mt-2 text-xs overflow-auto max-h-40 bg-white p-2 rounded">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default AdminApiTest;
