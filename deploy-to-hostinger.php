<?php
// Complete Hostinger Deployment Script
// Upload this to your public_html/api/ folder and run it
// URL: https://ebisera.com/api/deploy-to-hostinger.php

echo "<h2>🚀 Complete Hostinger Deployment</h2>";

echo "<h3>1. Creating proper Laravel 12 structure...</h3>";

// Create the correct index.php for Laravel 12
$correctIndexPHP = '<?php

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

define(\'LARAVEL_START\', microtime(true));

// CORS headers
header(\'Access-Control-Allow-Origin: *\');
header(\'Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS\');
header(\'Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept\');

if ($_SERVER[\'REQUEST_METHOD\'] === \'OPTIONS\') {
    http_response_code(200);
    exit();
}

// Check for maintenance mode
if (file_exists($maintenance = __DIR__.\'/storage/framework/maintenance.php\')) {
    require $maintenance;
}

// Register the Composer autoloader
require __DIR__.\'/vendor/autoload.php\';

// Bootstrap Laravel and handle the request
/** @var Application $app */
$app = require_once __DIR__.\'/bootstrap/app.php\';

$app->handleRequest(Request::capture());
';

if (file_put_contents('index.php', $correctIndexPHP)) {
    echo "<p style='color: green;'>✅ Created correct Laravel 12 index.php</p>";
} else {
    echo "<p style='color: red;'>❌ Failed to create index.php</p>";
}

// Create proper .htaccess
$htaccessContent = '<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Handle preflight OPTIONS requests
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

# CORS Headers
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Accept, Origin"
</IfModule>

Options -Indexes

<Files ".env">
    Order allow,deny
    Deny from all
</Files>';

if (file_put_contents('.htaccess', $htaccessContent)) {
    echo "<p style='color: green;'>✅ Created proper .htaccess</p>";
} else {
    echo "<p style='color: red;'>❌ Failed to create .htaccess</p>";
}

echo "<h3>2. Setting up Laravel environment...</h3>";

// Check if Laravel files exist
if (!file_exists('artisan')) {
    echo "<p style='color: red;'>❌ Laravel files not found. Please upload your Laravel files to this directory.</p>";
    exit;
}

// Set permissions
exec('chmod -R 755 storage 2>&1');
exec('chmod -R 755 bootstrap/cache 2>&1');
echo "<p style='color: green;'>✅ Permissions set</p>";

// Clear all caches
exec('php artisan config:clear 2>&1');
exec('php artisan route:clear 2>&1');
exec('php artisan cache:clear 2>&1');
exec('php artisan view:clear 2>&1');
echo "<p style='color: green;'>✅ All caches cleared</p>";

// Generate app key
exec('php artisan key:generate --force 2>&1');
echo "<p style='color: green;'>✅ App key generated</p>";

echo "<h3>3. Setting up database...</h3>";

// Run migrations
exec('php artisan migrate --force 2>&1', $migrateOutput, $migrateReturn);
if ($migrateReturn === 0) {
    echo "<p style='color: green;'>✅ Migrations completed</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Migration output:</p>";
    echo "<pre>" . implode("\n", array_slice($migrateOutput, -10)) . "</pre>";
}

// Seed database
exec('php artisan db:seed --force 2>&1', $seedOutput, $seedReturn);
if ($seedReturn === 0) {
    echo "<p style='color: green;'>✅ Database seeded</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Seeding output:</p>";
    echo "<pre>" . implode("\n", array_slice($seedOutput, -5)) . "</pre>";
}

// Create storage link
exec('php artisan storage:link 2>&1');
echo "<p style='color: green;'>✅ Storage link created</p>";

echo "<h3>4. Optimizing for production...</h3>";

// Cache config and routes
exec('php artisan config:cache 2>&1');
exec('php artisan route:cache 2>&1');
echo "<p style='color: green;'>✅ Production caches created</p>";

echo "<h3>5. Testing Laravel 12 setup...</h3>";

// Test route registration
exec('php artisan route:list --path=api 2>&1', $routeList, $routeReturn);
if ($routeReturn === 0) {
    $routeOutput = implode("\n", $routeList);
    if (strpos($routeOutput, 'api/cars') !== false) {
        echo "<p style='color: green;'>✅ API routes are properly registered</p>";
    } else {
        echo "<p style='color: red;'>❌ API routes not found</p>";
        echo "<pre>" . implode("\n", array_slice($routeList, 0, 10)) . "</pre>";
    }
} else {
    echo "<p style='color: red;'>❌ Route listing failed</p>";
}

// Test Laravel bootstrap
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    echo "<p style='color: green;'>✅ Laravel 12 bootstraps correctly</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Laravel bootstrap error: " . $e->getMessage() . "</p>";
}

echo "<h3>6. Final API tests...</h3>";

// Test API endpoints
$endpoints = ['cars', 'drivers'];
foreach ($endpoints as $endpoint) {
    $url = "https://ebisera.com/api/$endpoint";
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'ignore_errors' => true,
            'header' => "Accept: application/json\r\n"
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    if ($response !== false && strpos($response, '[') === 0) {
        echo "<p style='color: green;'>✅ $endpoint API returning JSON data</p>";
    } else {
        echo "<p style='color: red;'>❌ $endpoint API not working properly</p>";
    }
}

echo "<hr>";
echo "<h3>✅ Deployment Complete!</h3>";

echo "<h4>🧪 Test these URLs:</h4>";
echo "<ul>";
echo "<li><a href='https://ebisera.com/api/cars' target='_blank'>Cars API</a></li>";
echo "<li><a href='https://ebisera.com/api/drivers' target='_blank'>Drivers API</a></li>";
echo "<li><a href='https://ebisera.com' target='_blank'>Frontend</a></li>";
echo "</ul>";

echo "<h4>🎯 What was fixed:</h4>";
echo "<ul>";
echo "<li>✅ Proper Laravel 12 bootstrap structure</li>";
echo "<li>✅ Correct route registration</li>";
echo "<li>✅ Working API endpoints</li>";
echo "<li>✅ CORS headers for frontend access</li>";
echo "<li>✅ Production optimization</li>";
echo "</ul>";

echo "<p><strong>Your Park & Rent app should now be fully functional!</strong></p>";
echo "<p><strong>⚠️ Delete this file after testing!</strong></p>";
?>
