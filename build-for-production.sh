#!/bin/bash

echo "🚀 Building React app for production..."

# Install dependencies
npm install

# Copy production environment
cp .env.production .env

# Build the application
npm run build

echo "✅ Build complete!"
echo "📁 Upload the contents of the 'dist' folder to your hosting 'public_html' directory"
echo "🌐 Your app will be accessible at: https://yourdomain.com"

# Create a zip file for easy upload
echo "📦 Creating deployment package..."
cd dist
zip -r ../park-and-rent-frontend.zip .
cd ..

echo "✅ Deployment package created: park-and-rent-frontend.zip"
