<?php
// Run this script once on your Hostinger server after uploading Laravel files
// Access it via: https://yourdomain.com/api/server-setup.php

echo "<h2>🚀 Park & Rent Server Setup</h2>";

// Check if we're in the right directory
if (!file_exists('artisan')) {
    die("❌ Error: artisan file not found. Make sure you're in the Laravel root directory.");
}

echo "<h3>📋 Running Setup Commands...</h3>";

// Generate application key
echo "<p>🔑 Generating application key...</p>";
exec('php artisan key:generate --force 2>&1', $output1);
echo "<pre>" . implode("\n", $output1) . "</pre>";

// Clear and cache config
echo "<p>⚙️ Caching configuration...</p>";
exec('php artisan config:cache 2>&1', $output2);
echo "<pre>" . implode("\n", $output2) . "</pre>";

// Run migrations
echo "<p>🗄️ Running database migrations...</p>";
exec('php artisan migrate --force 2>&1', $output3);
echo "<pre>" . implode("\n", $output3) . "</pre>";

// Seed database
echo "<p>🌱 Seeding database...</p>";
exec('php artisan db:seed --force 2>&1', $output4);
echo "<pre>" . implode("\n", $output4) . "</pre>";

// Create storage link
echo "<p>🔗 Creating storage link...</p>";
exec('php artisan storage:link 2>&1', $output5);
echo "<pre>" . implode("\n", $output5) . "</pre>";

// Set permissions
echo "<p>🔒 Setting permissions...</p>";
exec('chmod -R 755 storage 2>&1', $output6);
exec('chmod -R 755 bootstrap/cache 2>&1', $output7);
echo "<pre>Storage and cache permissions set to 755</pre>";

echo "<h3>✅ Setup Complete!</h3>";
echo "<p><strong>⚠️ IMPORTANT: Delete this file (server-setup.php) after setup is complete for security!</strong></p>";
echo "<p>🌐 Your API should now be accessible at: <a href='/api/'>https://yourdomain.com/api/</a></p>";
echo "<p>🧪 Test endpoint: <a href='/api/cars'>https://yourdomain.com/api/cars</a></p>";
?>
