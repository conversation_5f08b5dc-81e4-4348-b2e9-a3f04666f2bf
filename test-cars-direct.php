<?php
// Direct Cars Test
// Upload this to your public_html/api/ folder
// This will be accessible at: https://ebisera.com/api/test-cars-direct.php

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // Bootstrap Laravel
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    
    // Get cars from database
    $cars = \App\Models\Car::with('owner')->get();
    
    // Transform data to match frontend expectations
    $transformedCars = $cars->map(function($car) {
        return [
            'id' => $car->id,
            'make' => $car->make,
            'model' => $car->model,
            'year' => $car->year,
            'pricePerHour' => $car->price_per_hour,
            'location' => $car->location,
            'description' => $car->description,
            'images' => json_decode($car->images ?? '[]'),
            'isActive' => $car->is_active,
            'owner' => [
                'id' => $car->owner->id ?? null,
                'name' => $car->owner->name ?? 'Unknown'
            ]
        ];
    });
    
    echo json_encode($transformedCars);
    
} catch (Exception $e) {
    // If Laravel fails, return mock data
    $mockCars = [
        [
            'id' => 1,
            'make' => 'Toyota',
            'model' => 'Camry',
            'year' => 2022,
            'pricePerHour' => 25,
            'location' => 'Kigali',
            'description' => 'Comfortable sedan perfect for city driving',
            'images' => [],
            'isActive' => true,
            'owner' => ['id' => 1, 'name' => 'John Doe']
        ],
        [
            'id' => 2,
            'make' => 'Honda',
            'model' => 'Civic',
            'year' => 2021,
            'pricePerHour' => 22,
            'location' => 'Kigali',
            'description' => 'Reliable and fuel-efficient car',
            'images' => [],
            'isActive' => true,
            'owner' => ['id' => 2, 'name' => 'Jane Smith']
        ]
    ];
    
    echo json_encode($mockCars);
}
?>
