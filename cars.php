<?php
// Mock Cars API Response
// Upload this to your public_html/api/ folder
// This will work as https://ebisera.com/api/cars.php

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Mock cars data
$cars = [
    [
        'id' => 1,
        'make' => 'Toyota',
        'model' => 'Camry',
        'year' => 2022,
        'pricePerHour' => 25,
        'location' => 'Kigali',
        'description' => 'Comfortable sedan perfect for city driving',
        'images' => ['car1.jpg'],
        'isActive' => true,
        'owner' => [
            'id' => 1,
            'name' => 'John <PERSON>'
        ]
    ],
    [
        'id' => 2,
        'make' => 'Honda',
        'model' => 'Civic',
        'year' => 2021,
        'pricePerHour' => 22,
        'location' => 'Kigali',
        'description' => 'Reliable and fuel-efficient car',
        'images' => ['car2.jpg'],
        'isActive' => true,
        'owner' => [
            'id' => 2,
            'name' => '<PERSON>'
        ]
    ],
    [
        'id' => 3,
        'make' => 'Nissan',
        'model' => 'Altima',
        'year' => 2023,
        'pricePerHour' => 28,
        'location' => 'Kigali',
        'description' => 'Modern car with latest features',
        'images' => ['car3.jpg'],
        'isActive' => true,
        'owner' => [
            'id' => 3,
            'name' => 'Bob Johnson'
        ]
    ]
];

echo json_encode($cars);
?>
