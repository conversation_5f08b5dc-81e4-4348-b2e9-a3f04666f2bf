import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Users, Car, UserRound, Calendar, CheckCircle, XCircle,
  AlertTriangle, BarChart2, DollarSign, Search, Filter
} from 'lucide-react';
import MainLayout from '../../components/layout/MainLayout';
import Button from '../../components/ui/Button';
import { useCars } from '../../context/CarContext';
import { useDrivers } from '../../context/DriverContext';
import { useBookings } from '../../context/BookingContext';
import { useAuth } from '../../context/AuthContext';
import { useAdmin } from '../../context/AdminContext';
import Spinner from '../../components/ui/Spinner';
import { User, Car as CarType, Driver, Booking } from '../../types';
import { apiClient } from '../../config/api';

// Admin role check - check if user has admin role
const isAdmin = (user: User | null) => {
  return user?.role === 'admin';
};

const AdminDashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { cars, updateCar } = useCars();
  const { drivers, updateDriver } = useDrivers();
  const { bookings, updateBookingStatus } = useBookings();
  const {
    stats,
    users,
    cars: adminCars,
    drivers: adminDrivers,
    bookings: adminBookings,
    isLoading: adminLoading,
    error: adminError,
    fetchDashboardStats,
    fetchUsers,
    fetchCars: fetchAdminCars,
    fetchDrivers: fetchAdminDrivers,
    fetchBookings: fetchAdminBookings,
    updateUserRole,
    verifyDriverLicense
  } = useAdmin();

  const [activeTab, setActiveTab] = useState<'overview' | 'users' | 'cars' | 'drivers' | 'bookings' | 'gps-requests' | 'settings'>('overview');
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Modal states
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showUserModal, setShowUserModal] = useState(false);
  const [showDriverModal, setShowDriverModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedDriver, setSelectedDriver] = useState<Driver | null>(null);

  // Form states
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');

  // Fetch data when component mounts
  useEffect(() => {
    if (isAdmin(user)) {
      fetchDashboardStats();
      fetchUsers();
      fetchAdminCars();
      fetchAdminDrivers();
      fetchAdminBookings();
    }
  }, [user]);

  // Handler functions
  const handleVerifyDriver = async (driverId: string, status: 'verified' | 'rejected') => {
    try {
      setIsLoading(true);
      await verifyDriverLicense(driverId, status);
      // Refresh drivers data
      await fetchAdminDrivers();
    } catch (error) {
      console.error('Failed to verify driver:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateUserRole = async (userId: string, newRole: string) => {
    try {
      setIsLoading(true);
      await updateUserRole(userId, newRole);
      // Refresh users data
      await fetchUsers();
    } catch (error) {
      console.error('Failed to update user role:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordChange = async () => {
    setPasswordError('');

    if (newPassword !== confirmPassword) {
      setPasswordError('Passwords do not match');
      return;
    }

    if (newPassword.length < 6) {
      setPasswordError('Password must be at least 6 characters');
      return;
    }

    try {
      setIsLoading(true);
      // Call API to change password
      await apiClient.post('/users/update-password', {
        current_password: currentPassword,
        new_password: newPassword,
        new_password_confirmation: confirmPassword
      });

      // Reset form and close modal
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      setShowPasswordModal(false);
      alert('Password changed successfully!');
    } catch (error: any) {
      setPasswordError(error.response?.data?.message || 'Failed to change password');
    } finally {
      setIsLoading(false);
    }
  };

  // Stats for overview - use real data from backend
  const totalUsers = stats?.users_count || 0;
  const totalCars = stats?.cars_count || 0;
  const totalDrivers = stats?.drivers_count || 0;
  const totalBookings = stats?.bookings_count || 0;
  const pendingVerifications = stats?.pending_driver_verifications || 0;

  // Filtered lists based on search and filters
  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredCars = cars.filter(car =>
    (car.make.toLowerCase().includes(searchQuery.toLowerCase()) ||
     car.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
     car.location.toLowerCase().includes(searchQuery.toLowerCase())) &&
    (statusFilter === 'all' ||
     (statusFilter === 'active' && car.isActive) ||
     (statusFilter === 'inactive' && !car.isActive))
  );

  const filteredDrivers = drivers.filter(driver =>
    (driver.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
     driver.location.toLowerCase().includes(searchQuery.toLowerCase())) &&
    (statusFilter === 'all' ||
     (statusFilter === 'active' && driver.isAvailable) ||
     (statusFilter === 'inactive' && !driver.isAvailable) ||
     (statusFilter === 'pending' && driver.licenseVerificationStatus === 'pending'))
  );

  const filteredBookings = bookings.filter(booking =>
    (statusFilter === 'all' || booking.status === statusFilter)
  );

  const handleVerifyLicense = async (userId: string, type: 'user' | 'driver', id: string, status: 'verified' | 'rejected') => {
    setIsLoading(true);
    try {
      if (type === 'driver') {
        await verifyDriverLicense(id, status);
      } else {
        // For user license verification, we would need a separate API endpoint
        console.log('User license verification not implemented yet');
      }
    } catch (error) {
      console.error('Failed to update verification status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateBookingStatus = async (bookingId: string, status: Booking['status']) => {
    setIsLoading(true);
    try {
      await updateBookingStatus(bookingId, status);
    } catch (error) {
      console.error('Failed to update booking status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!user || !isAdmin(user)) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-error-50 border border-error-200 rounded-lg p-4 text-center">
            <h2 className="text-xl font-medium text-error-800 mb-2">Access Denied</h2>
            <p className="text-error-600">
              You must be logged in as an administrator to access this page.
            </p>
            <div className="mt-4 text-sm text-gray-600">
              <p>Current user: {user?.name || 'Not logged in'}</p>
              <p>Role: {user?.role || 'No role'}</p>
              <p className="mt-2">Please login with admin credentials:</p>
              <p>Email: <EMAIL></p>
              <p>Password: password</p>
            </div>
            <Button
              className="mt-4"
              onClick={() => navigate('/login')}
            >
              Log In
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Admin Dashboard</h1>
            <p className="text-gray-600">
              Manage users, cars, drivers, and bookings.
            </p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
          <div className="flex flex-wrap border-b border-gray-200">
            <button
              className={`px-6 py-3 font-medium text-sm focus:outline-none ${
                activeTab === 'overview'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('overview')}
            >
              <BarChart2 size={16} className="inline-block mr-1" />
              Overview
            </button>
            <button
              className={`px-6 py-3 font-medium text-sm focus:outline-none ${
                activeTab === 'users'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('users')}
            >
              <Users size={16} className="inline-block mr-1" />
              Users
            </button>
            <button
              className={`px-6 py-3 font-medium text-sm focus:outline-none ${
                activeTab === 'cars'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('cars')}
            >
              <Car size={16} className="inline-block mr-1" />
              Cars
            </button>
            <button
              className={`px-6 py-3 font-medium text-sm focus:outline-none ${
                activeTab === 'drivers'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('drivers')}
            >
              <UserRound size={16} className="inline-block mr-1" />
              Drivers
            </button>
            <button
              className={`px-6 py-3 font-medium text-sm focus:outline-none ${
                activeTab === 'bookings'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('bookings')}
            >
              <Calendar size={16} className="inline-block mr-1" />
              Bookings
            </button>
          </div>

          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-6">Platform Overview</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div className="bg-blue-50 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-blue-800">Total Users</h3>
                    <Users size={24} className="text-blue-500" />
                  </div>
                  <p className="text-3xl font-bold text-blue-600">{totalUsers}</p>
                  <div className="mt-2 text-sm text-blue-600">
                    <span className="font-medium">{users.filter(u => u.role === 'client').length}</span> Clients,
                    <span className="font-medium ml-1">{users.filter(u => u.role === 'owner').length}</span> Owners,
                    <span className="font-medium ml-1">{users.filter(u => u.role === 'driver').length}</span> Drivers
                  </div>
                </div>

                <div className="bg-green-50 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-green-800">Total Cars</h3>
                    <Car size={24} className="text-green-500" />
                  </div>
                  <p className="text-3xl font-bold text-green-600">{totalCars}</p>
                  <div className="mt-2 text-sm text-green-600">
                    <span className="font-medium">{adminCars.filter(c => c.isActive).length}</span> Active,
                    <span className="font-medium ml-1">{adminCars.filter(c => !c.isActive).length}</span> Inactive
                  </div>
                </div>

                <div className="bg-purple-50 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-purple-800">Total Drivers</h3>
                    <UserRound size={24} className="text-purple-500" />
                  </div>
                  <p className="text-3xl font-bold text-purple-600">{totalDrivers}</p>
                  <div className="mt-2 text-sm text-purple-600">
                    <span className="font-medium">{adminDrivers.filter(d => d.isAvailable).length}</span> Available,
                    <span className="font-medium ml-1">{adminDrivers.filter(d => !d.isAvailable).length}</span> Unavailable
                  </div>
                </div>

                <div className="bg-yellow-50 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-yellow-800">Total Bookings</h3>
                    <Calendar size={24} className="text-yellow-500" />
                  </div>
                  <p className="text-3xl font-bold text-yellow-600">{totalBookings}</p>
                  <div className="mt-2 text-sm text-yellow-600">
                    <span className="font-medium">{stats?.completed_bookings || 0}</span> Completed,
                    <span className="font-medium ml-1">{stats?.pending_bookings || 0}</span> Pending
                  </div>
                </div>
              </div>

              <div className="bg-orange-50 rounded-lg p-6 mb-8">
                <div className="flex items-center mb-4">
                  <AlertTriangle size={24} className="text-orange-500 mr-2" />
                  <h3 className="text-lg font-medium text-orange-800">Pending Verifications</h3>
                </div>
                <p className="text-3xl font-bold text-orange-600 mb-2">{pendingVerifications}</p>
                <p className="text-sm text-orange-700">
                  {pendingVerifications} users are waiting for license verification. Review these to maintain platform safety.
                </p>
                <Button
                  variant="outline"
                  className="mt-4 border-orange-500 text-orange-700 hover:bg-orange-100"
                  onClick={() => setActiveTab('users')}
                >
                  Review Verifications
                </Button>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Recent Activity</h3>
                <div className="space-y-4">
                  {(stats?.recent_bookings || []).slice(0, 5).map((booking, index) => (
                    <div key={index} className="flex items-start">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                        booking.status === 'completed' ? 'bg-success-100' :
                        booking.status === 'pending' ? 'bg-yellow-100' :
                        booking.status === 'confirmed' ? 'bg-blue-100' : 'bg-error-100'
                      }`}>
                        <Calendar size={16} className={`${
                          booking.status === 'completed' ? 'text-success-600' :
                          booking.status === 'pending' ? 'text-yellow-600' :
                          booking.status === 'confirmed' ? 'text-blue-600' : 'text-error-600'
                        }`} />
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">
                          New {booking.itemType} booking ({booking.status})
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(booking.createdAt).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Users Tab */}
          {activeTab === 'users' && (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">Users Management</h2>

                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search size={16} className="text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search users..."
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        User
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Role
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Joined
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        License Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredUsers.map((user) => (
                      <tr key={user.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                              {user.licenseImageUrl ? (
                                <img
                                  src={user.licenseImageUrl}
                                  alt={user.name}
                                  className="h-10 w-10 rounded-full object-cover"
                                />
                              ) : (
                                <UserRound size={20} className="text-gray-500" />
                              )}
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {user.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {user.email}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            user.role === 'client'
                              ? 'bg-blue-100 text-blue-800'
                              : user.role === 'owner'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-purple-100 text-purple-800'
                          }`}>
                            {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(user.createdAt).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {user.licenseVerificationStatus ? (
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              user.licenseVerificationStatus === 'verified'
                                ? 'bg-success-100 text-success-800'
                                : user.licenseVerificationStatus === 'pending'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-error-100 text-error-800'
                            }`}>
                              {user.licenseVerificationStatus.charAt(0).toUpperCase() +
                               user.licenseVerificationStatus.slice(1)}
                            </span>
                          ) : (
                            <span className="text-gray-500 text-sm">N/A</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          {user.licenseVerificationStatus === 'pending' && (
                            <div className="flex space-x-2">
                              <button
                                className="text-success-600 hover:text-success-800"
                                onClick={() => handleVerifyLicense(user.id, 'user', user.id, 'verified')}
                              >
                                <CheckCircle size={18} />
                              </button>
                              <button
                                className="text-error-600 hover:text-error-800"
                                onClick={() => handleVerifyLicense(user.id, 'user', user.id, 'rejected')}
                              >
                                <XCircle size={18} />
                              </button>
                            </div>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Cars Tab */}
          {activeTab === 'cars' && (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">Cars Management</h2>
                <div className="text-sm text-gray-600">
                  Total Cars: {adminCars.length}
                </div>
              </div>

              {adminLoading ? (
                <div className="flex justify-center py-8">
                  <Spinner size="lg" />
                </div>
              ) : adminError ? (
                <div className="bg-error-50 border border-error-200 rounded-lg p-4 text-center">
                  <p className="text-error-600">{adminError}</p>
                </div>
              ) : adminCars.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Car</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Owner</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price/Hour</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {adminCars.map((car) => (
                        <tr key={car.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                  <Car className="h-5 w-5 text-gray-600" />
                                </div>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">
                                  {car.make} {car.model}
                                </div>
                                <div className="text-sm text-gray-500">{car.year}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            Owner #{car.ownerId}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {car.location}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${car.pricePerHour}/hr
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              car.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {car.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <Button size="sm" variant="outline">
                              View Details
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Car size={48} className="mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">No cars found</p>
                </div>
              )}
            </div>
          )}

          {/* Drivers Tab */}
          {activeTab === 'drivers' && (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">Drivers Management</h2>
                <div className="text-sm text-gray-600">
                  Total Drivers: {adminDrivers.length}
                </div>
              </div>

              {adminLoading ? (
                <div className="flex justify-center py-8">
                  <Spinner size="lg" />
                </div>
              ) : adminError ? (
                <div className="bg-error-50 border border-error-200 rounded-lg p-4 text-center">
                  <p className="text-error-600">{adminError}</p>
                </div>
              ) : adminDrivers.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Driver</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Experience</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price/Hour</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">License Status</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {adminDrivers.map((driver) => (
                        <tr key={driver.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                  <UserRound className="h-5 w-5 text-gray-600" />
                                </div>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">
                                  {driver.name}
                                </div>
                                <div className="text-sm text-gray-500">Age: {driver.age}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {driver.experience} years
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {driver.location}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${driver.pricePerHour}/hr
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              driver.licenseVerificationStatus === 'verified' ? 'bg-green-100 text-green-800' :
                              driver.licenseVerificationStatus === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {driver.licenseVerificationStatus}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-2">
                              <Button size="sm" variant="outline">
                                View Details
                              </Button>
                              {driver.licenseVerificationStatus === 'pending' && (
                                <Button size="sm" variant="primary">
                                  Verify
                                </Button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <UserRound size={48} className="mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">No drivers found</p>
                </div>
              )}
            </div>
          )}

          {/* Bookings Tab */}
          {activeTab === 'bookings' && (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">Bookings Management</h2>
                <div className="text-sm text-gray-600">
                  Total Bookings: {adminBookings.length}
                </div>
              </div>

              {adminLoading ? (
                <div className="flex justify-center py-8">
                  <Spinner size="lg" />
                </div>
              ) : adminError ? (
                <div className="bg-error-50 border border-error-200 rounded-lg p-4 text-center">
                  <p className="text-error-600">{adminError}</p>
                </div>
              ) : adminBookings.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Booking ID</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Price</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {adminBookings.map((booking) => (
                        <tr key={booking.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            #{booking.id}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            User #{booking.userId}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {booking.itemType} #{booking.itemId}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {new Date(booking.startTime).toLocaleDateString()} - {new Date(booking.endTime).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${booking.totalPrice}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              booking.status === 'completed' ? 'bg-green-100 text-green-800' :
                              booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              booking.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                              'bg-blue-100 text-blue-800'
                            }`}>
                              {booking.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <Button size="sm" variant="outline">
                              View Details
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar size={48} className="mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">No bookings found</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default AdminDashboardPage;
