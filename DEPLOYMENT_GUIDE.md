# 🚀 Park & Rent - Hostinger Deployment Guide

## 📋 Prerequisites
- Hostinger hosting account (Business or Premium plan)
- Domain name configured
- FTP/File Manager access

## 🎯 Step-by-Step Deployment

### 1. 🗄️ Database Setup
1. **Login to Hostinger hPanel**
2. **Go to Databases → MySQL Databases**
3. **Create new database:**
   - Database name: `parkandrent_db`
   - Username: `parkandrent_user`
   - Password: `[Generate strong password]`
4. **Save credentials** - You'll need them later!

### 2. 🔧 Backend Deployment (Laravel API)

#### A. Prepare Files Locally
```bash
# Navigate to Laravel directory
cd park-and-rent-api

# Install dependencies
composer install --optimize-autoloader --no-dev

# Generate app key
php artisan key:generate
```

#### B. Upload to Hostinger
1. **Compress** the `park-and-rent-api` folder
2. **Upload via File Manager:**
   - Go to hPanel → File Manager
   - Navigate to `public_html`
   - Create folder: `api`
   - Upload and extract Laravel files to `api` folder

#### C. Configure Environment
1. **Copy** `.env.production` to `.env` in the `api` folder
2. **Update** `.env` with your details:
   ```env
   APP_URL=https://yourdomain.com
   DB_DATABASE=parkandrent_db
   DB_USERNAME=parkandrent_user
   DB_PASSWORD=your_database_password
   MAIL_FROM_ADDRESS=<EMAIL>
   FRONTEND_URL=https://yourdomain.com
   ```

#### D. Run Setup
1. **Upload** `server-setup.php` to your `api` folder
2. **Visit** `https://yourdomain.com/api/server-setup.php`
3. **Follow** the setup instructions
4. **Delete** `server-setup.php` after completion

### 3. 🎨 Frontend Deployment (React App)

#### A. Build for Production
```bash
# Install dependencies
npm install

# Copy production environment
cp .env.production .env

# Update .env.production with your domain
VITE_API_URL=https://yourdomain.com/api

# Build the app
npm run build
```

#### B. Upload to Hostinger
1. **Navigate** to the `dist` folder
2. **Select all files** in the dist folder
3. **Upload** to `public_html` (root directory)
4. **Extract** if uploaded as zip

### 4. 🔧 Final Configuration

#### A. Update Domain References
Replace `yourdomain.com` with your actual domain in:
- `.env` (backend)
- `.env.production` (frontend)
- CORS configuration

#### B. SSL Certificate
1. **Go to** hPanel → SSL
2. **Enable** SSL for your domain
3. **Force HTTPS** redirect

#### C. Test Your Application
1. **Frontend:** `https://yourdomain.com`
2. **API:** `https://yourdomain.com/api/cars`
3. **Admin:** `https://yourdomain.com/admin`

## 🧪 Testing Checklist

- [ ] Homepage loads correctly
- [ ] Car listings display
- [ ] Driver listings display
- [ ] User registration works
- [ ] User login works
- [ ] Admin panel accessible
- [ ] API endpoints respond
- [ ] Database connections work
- [ ] Email functionality works

## 🔧 Troubleshooting

### Common Issues:

**1. 500 Internal Server Error**
- Check file permissions (755 for folders, 644 for files)
- Verify `.env` configuration
- Check error logs in hPanel

**2. CORS Errors**
- Verify CORS configuration in `config/cors.php`
- Ensure frontend URL is in allowed origins

**3. Database Connection Failed**
- Double-check database credentials
- Ensure database exists
- Verify host is `localhost`

**4. API Not Found**
- Check `.htaccess` file in API folder
- Verify mod_rewrite is enabled
- Check API URL configuration

### File Permissions:
```bash
# Set correct permissions
chmod -R 755 storage
chmod -R 755 bootstrap/cache
chmod 644 .env
```

## 📞 Support

If you encounter issues:
1. Check Hostinger knowledge base
2. Contact Hostinger support
3. Review Laravel/React documentation
4. Check browser console for errors

## 🎉 Success!

Your Park & Rent application should now be live at:
- **Website:** https://yourdomain.com
- **Admin Panel:** https://yourdomain.com/admin
- **API:** https://yourdomain.com/api

**Default Admin Credentials:**
- Email: <EMAIL>
- Password: password

**⚠️ Remember to change the admin password after first login!**
