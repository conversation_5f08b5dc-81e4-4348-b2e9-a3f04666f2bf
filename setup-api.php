<?php
// Complete API Setup Script
// Upload this to your public_html/api/ folder
// URL: https://ebisera.com/api/setup-api.php

echo "<h2>🚀 Complete API Setup</h2>";

// Check if <PERSON><PERSON> exists
if (!file_exists('artisan')) {
    die("<p style='color: red;'>❌ Laravel not found. Make sure Laravel files are uploaded to this directory.</p>");
}

echo "<h3>1. 🔧 Setting up Lara<PERSON> entry point...</h3>";

// Create proper index.php
$indexContent = '<?php

use Illuminate\Contracts\Http\Kernel;
use Illuminate\Http\Request;

define(\'LARAVEL_START\', microtime(true));

// Register The Auto Loader
require __DIR__.\'/vendor/autoload.php\';

// Bootstrap Laravel
$app = require_once __DIR__.\'/bootstrap/app.php\';

$kernel = $app->make(Kernel::class);

$response = $kernel->handle(
    $request = Request::capture()
)->send();

$kernel->terminate($request, $response);
';

file_put_contents('index.php', $indexContent);
echo "<p style='color: green;'>✅ Created index.php</p>";

// Create .htaccess
$htaccess = '<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
    
    # Redirect Trailing Slashes
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]
    
    # Send Requests To Front Controller
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

# CORS Headers
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
</IfModule>

Options -Indexes';

file_put_contents('.htaccess', $htaccess);
echo "<p style='color: green;'>✅ Created .htaccess</p>";

echo "<h3>2. 🔒 Setting permissions...</h3>";
exec('chmod -R 755 storage');
exec('chmod -R 755 bootstrap/cache');
echo "<p style='color: green;'>✅ Permissions set</p>";

echo "<h3>3. ⚙️ Configuring Laravel...</h3>";
exec('php artisan config:clear');
exec('php artisan route:clear');
exec('php artisan view:clear');
exec('php artisan cache:clear');
echo "<p style='color: green;'>✅ Caches cleared</p>";

// Generate app key if needed
if (file_exists('.env')) {
    $env = file_get_contents('.env');
    if (strpos($env, 'APP_KEY=') === false || strpos($env, 'APP_KEY=base64:') === false) {
        exec('php artisan key:generate --force');
        echo "<p style='color: green;'>✅ App key generated</p>";
    }
}

echo "<h3>4. 🗄️ Setting up database...</h3>";
exec('php artisan migrate --force 2>&1', $migrateOutput, $migrateReturn);
if ($migrateReturn === 0) {
    echo "<p style='color: green;'>✅ Migrations completed</p>";
} else {
    echo "<p style='color: red;'>❌ Migration failed:</p>";
    echo "<pre>" . implode("\n", $migrateOutput) . "</pre>";
}

exec('php artisan db:seed --force 2>&1', $seedOutput, $seedReturn);
if ($seedReturn === 0) {
    echo "<p style='color: green;'>✅ Database seeded</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Seeding result:</p>";
    echo "<pre>" . implode("\n", $seedOutput) . "</pre>";
}

echo "<h3>5. 🔗 Creating storage link...</h3>";
exec('php artisan storage:link 2>&1', $linkOutput);
echo "<p style='color: green;'>✅ Storage link created</p>";

echo "<h3>6. 🎯 Caching for production...</h3>";
exec('php artisan config:cache');
exec('php artisan route:cache');
echo "<p style='color: green;'>✅ Production caches created</p>";

echo "<h3>7. 🧪 Testing API endpoints...</h3>";

// Test basic Laravel
echo "<p>Testing Laravel bootstrap...</p>";
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    echo "<p style='color: green;'>✅ Laravel bootstraps successfully</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Laravel bootstrap failed: " . $e->getMessage() . "</p>";
}

// Test routes
echo "<p>Testing routes...</p>";
exec('php artisan route:list 2>&1', $routeOutput);
if (strpos(implode("\n", $routeOutput), 'api/cars') !== false) {
    echo "<p style='color: green;'>✅ API routes found</p>";
} else {
    echo "<p style='color: red;'>❌ API routes not found</p>";
    echo "<pre>" . implode("\n", array_slice($routeOutput, 0, 20)) . "</pre>";
}

echo "<hr>";
echo "<h3>✅ Setup Complete!</h3>";
echo "<p><strong>Test these URLs:</strong></p>";
echo "<ul>";
echo "<li><a href='https://ebisera.com/api/' target='_blank'>API Root</a></li>";
echo "<li><a href='https://ebisera.com/api/cars' target='_blank'>Cars API</a></li>";
echo "<li><a href='https://ebisera.com/api/drivers' target='_blank'>Drivers API</a></li>";
echo "</ul>";

echo "<p><strong>⚠️ IMPORTANT:</strong></p>";
echo "<ul>";
echo "<li>Delete this file after setup</li>";
echo "<li>Test your frontend at <a href='https://ebisera.com'>https://ebisera.com</a></li>";
echo "<li>Check browser console for any remaining errors</li>";
echo "</ul>";
?>
