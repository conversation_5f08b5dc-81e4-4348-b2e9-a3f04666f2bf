# 🚀 Complete Hostinger Deployment Plan for Park & Rent

## 📋 Current Issue Analysis

### ❌ What's Wrong:
1. **Laravel 12 structure not properly deployed**
2. **Laravel's public/index.php not used as API entry point**
3. **Incorrect path mappings in index.php**
4. **Routes not loading because bootstrap is wrong**

### ✅ What We Need:
1. **Proper Laravel 12 deployment structure**
2. **Correct public/index.php as API entry point**
3. **Fixed path mappings for Hostinger**
4. **Working route registration**

## 🎯 Step-by-Step Deployment Plan

### **STEP 1: Prepare Laravel Backend**

#### A. Create Deployment Structure Script
```bash
# Run this locally to prepare <PERSON><PERSON> for deployment
cd park-and-rent-api

# Install production dependencies
composer install --optimize-autoloader --no-dev

# Clear all caches
php artisan config:clear
php artisan route:clear
php artisan cache:clear
php artisan view:clear

# Generate optimized autoloader
composer dump-autoload --optimize

# Create deployment package
cd ..
zip -r laravel-backend.zip park-and-rent-api/ -x "park-and-rent-api/node_modules/*" "park-and-rent-api/.git/*"
```

#### B. Create Production Environment File
Create `park-and-rent-api/.env.production`:
```env
APP_NAME="Park & Rent"
APP_ENV=production
APP_KEY=base64:YOUR_APP_KEY_HERE
APP_DEBUG=false
APP_URL=https://ebisera.com

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_hostinger_db_name
DB_USERNAME=your_hostinger_db_user
DB_PASSWORD=your_hostinger_db_password

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=public
QUEUE_CONNECTION=database

CACHE_STORE=file
SESSION_DRIVER=file
SESSION_LIFETIME=120

MAIL_MAILER=smtp
MAIL_HOST=smtp.hostinger.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Park & Rent"

SANCTUM_STATEFUL_DOMAINS=ebisera.com,www.ebisera.com
SESSION_DOMAIN=.ebisera.com
FRONTEND_URL=https://ebisera.com
```

### **STEP 2: Prepare React Frontend**

#### A. Update Environment for Production
Create `.env.production`:
```env
VITE_API_URL=https://ebisera.com/api
VITE_APP_NAME="Park & Rent"
VITE_APP_ENV=production
```

#### B. Build for Production
```bash
# Install dependencies
npm install

# Copy production environment
cp .env.production .env

# Build for production
npm run build

# Create deployment package
cd dist
zip -r ../frontend-build.zip .
cd ..
```

### **STEP 3: Deploy to Hostinger**

#### A. Upload Laravel Backend
1. **Upload** `laravel-backend.zip` to your hosting
2. **Extract** to a temporary folder
3. **Move contents** to `public_html/api/` but with this structure:

```
public_html/api/
├── index.php (from park-and-rent-api/public/index.php - MODIFIED)
├── .htaccess (custom for API)
├── laravel/ (all other Laravel files)
│   ├── app/
│   ├── bootstrap/
│   ├── config/
│   ├── database/
│   ├── routes/
│   ├── storage/
│   ├── vendor/
│   ├── artisan
│   └── .env
```

#### B. Create Correct API index.php
Create `public_html/api/index.php`:
```php
<?php

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

define('LARAVEL_START', microtime(true));

// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Check for maintenance mode
if (file_exists($maintenance = __DIR__.'/laravel/storage/framework/maintenance.php')) {
    require $maintenance;
}

// Register the Composer autoloader
require __DIR__.'/laravel/vendor/autoload.php';

// Bootstrap Laravel and handle the request
/** @var Application $app */
$app = require_once __DIR__.'/laravel/bootstrap/app.php';

$app->handleRequest(Request::capture());
```

#### C. Create API .htaccess
Create `public_html/api/.htaccess`:
```apache
<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Handle preflight OPTIONS requests
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

# CORS Headers
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Accept, Origin"
</IfModule>

Options -Indexes

<Files ".env">
    Order allow,deny
    Deny from all
</Files>
```

#### D. Upload React Frontend
1. **Upload** `frontend-build.zip` to `public_html/`
2. **Extract** and replace existing files
3. **Ensure** index.html is in the root of public_html

### **STEP 4: Configure Laravel on Server**

#### A. Set Environment
1. **Copy** `.env.production` to `public_html/api/laravel/.env`
2. **Update** database credentials in `.env`

#### B. Run Setup Commands
Create `public_html/api/setup.php` and run it:
```php
<?php
// Change to Laravel directory
chdir(__DIR__ . '/laravel');

// Run Laravel setup commands
exec('php artisan key:generate --force 2>&1', $output1);
exec('php artisan config:cache 2>&1', $output2);
exec('php artisan route:cache 2>&1', $output3);
exec('php artisan migrate --force 2>&1', $output4);
exec('php artisan db:seed --force 2>&1', $output5);
exec('php artisan storage:link 2>&1', $output6);

echo "Setup complete!";
// Delete this file after running
?>
```

### **STEP 5: Test Deployment**

#### A. Test API Endpoints
- `https://ebisera.com/api/cars` - Should return JSON
- `https://ebisera.com/api/drivers` - Should return JSON
- `https://ebisera.com/api/login` - Should accept POST

#### B. Test Frontend
- `https://ebisera.com` - Should load homepage
- `https://ebisera.com/cars` - Should load cars with data
- Browser console should show no 404 errors

## 🔧 Key Differences from Previous Attempts

1. **Correct Laravel 12 structure** - Uses proper bootstrap method
2. **Proper path mapping** - Laravel files in subdirectory with correct paths
3. **Working route registration** - Routes will load properly
4. **Production optimization** - Cached routes and config
5. **CORS handling** - Proper headers for API access

## 📞 Troubleshooting

If still not working:
1. Check Laravel logs in `api/laravel/storage/logs/`
2. Verify database connection
3. Ensure file permissions (755 for directories, 644 for files)
4. Check PHP version compatibility (8.1+)

This plan addresses the root cause: **improper Laravel 12 deployment structure**.
