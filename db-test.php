<?php
// Database Connection Test Script
// Upload this to your public_html/api/ folder and access via browser
// URL: https://yourdomain.com/api/db-test.php

echo "<h2>🔍 Database Connection Test</h2>";

// Load Laravel environment
if (file_exists('.env')) {
    $env = file_get_contents('.env');
    $lines = explode("\n", $env);
    $config = [];
    
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && !str_starts_with(trim($line), '#')) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
    
    echo "<h3>📋 Environment Configuration:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Setting</th><th>Value</th></tr>";
    echo "<tr><td>DB_HOST</td><td>" . ($config['DB_HOST'] ?? 'NOT SET') . "</td></tr>";
    echo "<tr><td>DB_PORT</td><td>" . ($config['DB_PORT'] ?? 'NOT SET') . "</td></tr>";
    echo "<tr><td>DB_DATABASE</td><td>" . ($config['DB_DATABASE'] ?? 'NOT SET') . "</td></tr>";
    echo "<tr><td>DB_USERNAME</td><td>" . ($config['DB_USERNAME'] ?? 'NOT SET') . "</td></tr>";
    echo "<tr><td>DB_PASSWORD</td><td>" . (isset($config['DB_PASSWORD']) ? '***SET***' : 'NOT SET') . "</td></tr>";
    echo "<tr><td>APP_URL</td><td>" . ($config['APP_URL'] ?? 'NOT SET') . "</td></tr>";
    echo "</table>";
    
    // Test database connection
    echo "<h3>🔌 Testing Database Connection:</h3>";
    
    $host = $config['DB_HOST'] ?? 'localhost';
    $port = $config['DB_PORT'] ?? '3306';
    $database = $config['DB_DATABASE'] ?? '';
    $username = $config['DB_USERNAME'] ?? '';
    $password = $config['DB_PASSWORD'] ?? '';
    
    try {
        $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
        
        echo "<p style='color: green;'>✅ <strong>Database connection successful!</strong></p>";
        
        // Test if tables exist
        echo "<h3>📊 Checking Tables:</h3>";
        $tables = ['users', 'cars', 'drivers', 'bookings', 'gps_installation_requests'];
        
        foreach ($tables as $table) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                $result = $stmt->fetch();
                echo "<p style='color: green;'>✅ Table '$table' exists with {$result['count']} records</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Table '$table' not found or error: " . $e->getMessage() . "</p>";
            }
        }
        
        // Test API endpoint
        echo "<h3>🌐 Testing API Endpoints:</h3>";
        $api_url = ($config['APP_URL'] ?? 'https://yourdomain.com') . '/api/cars';
        echo "<p>Testing: <a href='$api_url' target='_blank'>$api_url</a></p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ <strong>Database connection failed!</strong></p>";
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
        
        echo "<h3>🔧 Common Solutions:</h3>";
        echo "<ul>";
        echo "<li>Check if database name, username, and password are correct</li>";
        echo "<li>Ensure the database exists in your Hostinger panel</li>";
        echo "<li>Verify DB_HOST is set to 'localhost'</li>";
        echo "<li>Check if your hosting plan supports MySQL</li>";
        echo "</ul>";
    }
    
} else {
    echo "<p style='color: red;'>❌ .env file not found!</p>";
    echo "<p>Make sure you've uploaded and configured the .env file in your API directory.</p>";
}

echo "<hr>";
echo "<h3>📁 File Structure Check:</h3>";
echo "<p>Current directory: " . getcwd() . "</p>";
echo "<p>Files in current directory:</p>";
echo "<ul>";
$files = scandir('.');
foreach ($files as $file) {
    if ($file != '.' && $file != '..') {
        echo "<li>$file</li>";
    }
}
echo "</ul>";

echo "<hr>";
echo "<p><strong>⚠️ IMPORTANT: Delete this file after testing for security!</strong></p>";
?>
