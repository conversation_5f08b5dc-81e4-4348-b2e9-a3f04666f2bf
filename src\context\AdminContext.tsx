import React, { createContext, useContext, useState, useEffect } from 'react';
import { User, Car, Driver, Booking } from '../types';
import { apiClient, API_ENDPOINTS } from '../config/api';

interface AdminStats {
  users_count: number;
  cars_count: number;
  drivers_count: number;
  bookings_count: number;
  pending_bookings: number;
  completed_bookings: number;
  cancelled_bookings: number;
  pending_driver_verifications: number;
  revenue: number;
  recent_bookings: Booking[];
  recent_users: User[];
}

interface GpsInstallationRequest {
  id: string;
  user_id: string;
  car_id: string;
  car_make: string;
  car_model: string;
  car_year: string;
  license_plate: string;
  reason: string;
  contact_phone: string;
  preferred_installation_date?: string;
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  admin_notes?: string;
  approved_at?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
  user: User;
  car: Car;
}

interface AdminContextType {
  stats: AdminStats | null;
  users: User[];
  cars: Car[];
  drivers: Driver[];
  bookings: Booking[];
  gpsRequests: GpsInstallationRequest[];
  isLoading: boolean;
  error: string | null;
  fetchDashboardStats: () => Promise<void>;
  fetchUsers: () => Promise<void>;
  fetchCars: () => Promise<void>;
  fetchDrivers: () => Promise<void>;
  fetchBookings: () => Promise<void>;
  fetchGpsRequests: () => Promise<void>;
  updateUserRole: (userId: string, role: string) => Promise<void>;
  verifyDriverLicense: (driverId: string, status: string) => Promise<void>;
  updateGpsRequestStatus: (requestId: string, status: string, adminNotes?: string) => Promise<void>;
}

const AdminContext = createContext<AdminContextType | undefined>(undefined);

export const useAdmin = () => {
  const context = useContext(AdminContext);
  if (!context) {
    throw new Error('useAdmin must be used within an AdminProvider');
  }
  return context;
};

export const AdminProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [cars, setCars] = useState<Car[]>([]);
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [gpsRequests, setGpsRequests] = useState<GpsInstallationRequest[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardStats = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiClient.get(API_ENDPOINTS.ADMIN.DASHBOARD);
      setStats(response.data);
    } catch (err: any) {
      console.error('Error fetching dashboard stats:', err);
      setError(`Failed to fetch dashboard statistics: ${err.response?.status || err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchUsers = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiClient.get(API_ENDPOINTS.ADMIN.USERS);
      setUsers(response.data);
    } catch (err: any) {
      console.error('Error fetching users:', err);
      setError(`Failed to fetch users: ${err.response?.status || err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCars = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiClient.get(API_ENDPOINTS.ADMIN.CARS);
      setCars(response.data);
    } catch (err) {
      console.error('Error fetching cars:', err);
      setError('Failed to fetch cars');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchDrivers = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiClient.get(API_ENDPOINTS.ADMIN.DRIVERS);
      // Transform the API response to match our Driver type
      const fetchedDrivers: Driver[] = response.data.map((driver: any) => ({
        id: driver.id.toString(),
        userId: driver.user_id.toString(),
        name: driver.name,
        age: driver.age,
        experience: driver.experience,
        profileImage: driver.profile_image || '',
        licenseNumber: driver.license_number,
        licenseVerificationStatus: driver.license_verification_status,
        location: driver.location,
        pricePerHour: driver.price_per_hour,
        rating: Number(driver.rating || 0),
        reviews: Number(driver.reviews || 0),
        specialties: driver.specialties || [],
        availabilityNotes: driver.availability_notes || '',
        isAvailable: driver.is_available,
        createdAt: driver.created_at,
      }));
      setDrivers(fetchedDrivers);
    } catch (err) {
      console.error('Error fetching drivers:', err);
      setError('Failed to fetch drivers');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchBookings = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiClient.get(API_ENDPOINTS.ADMIN.BOOKINGS);
      setBookings(response.data);
    } catch (err) {
      console.error('Error fetching bookings:', err);
      setError('Failed to fetch bookings');
    } finally {
      setIsLoading(false);
    }
  };

  const updateUserRole = async (userId: string, role: string) => {
    try {
      await apiClient.post(`/admin/users/${userId}/update-role`, { role });
      // Refresh users list
      await fetchUsers();
    } catch (err) {
      console.error('Error updating user role:', err);
      throw new Error('Failed to update user role');
    }
  };

  const verifyDriverLicense = async (driverId: string, status: string) => {
    try {
      await apiClient.post(`/admin/drivers/${driverId}/verify-license`, { status });
      // Refresh drivers list
      await fetchDrivers();
    } catch (err) {
      console.error('Error verifying driver license:', err);
      throw new Error('Failed to verify driver license');
    }
  };

  const fetchGpsRequests = async () => {
    try {
      const response = await apiClient.get('/admin/gps-requests');
      setGpsRequests(response.data);
    } catch (err) {
      console.error('Error fetching GPS requests:', err);
      throw new Error('Failed to fetch GPS requests');
    }
  };

  const updateGpsRequestStatus = async (requestId: string, status: string, adminNotes?: string) => {
    try {
      await apiClient.post(`/admin/gps-requests/${requestId}/update-status`, {
        status,
        admin_notes: adminNotes
      });
      // Refresh GPS requests list
      await fetchGpsRequests();
    } catch (err) {
      console.error('Error updating GPS request status:', err);
      throw new Error('Failed to update GPS request status');
    }
  };



  const value = {
    stats,
    users,
    cars,
    drivers,
    bookings,
    gpsRequests,
    isLoading,
    error,
    fetchDashboardStats,
    fetchUsers,
    fetchCars,
    fetchDrivers,
    fetchBookings,
    fetchGpsRequests,
    updateUserRole,
    verifyDriverLicense,
    updateGpsRequestStatus,
  };

  return <AdminContext.Provider value={value}>{children}</AdminContext.Provider>;
};

export default AdminContext;
