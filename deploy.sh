#!/bin/bash

echo "🚀 Park & Rent - Hostinger Deployment Preparation"
echo "=================================================="

# Get domain from user
read -p "Enter your domain name (e.g., parkandrent.com): " DOMAIN

if [ -z "$DOMAIN" ]; then
    echo "❌ Domain name is required!"
    exit 1
fi

echo "🔧 Configuring for domain: $DOMAIN"

# Update environment files
echo "📝 Updating environment configurations..."

# Update backend .env.production
sed -i "s/yourdomain.com/$DOMAIN/g" park-and-rent-api/.env.production

# Update frontend .env.production
sed -i "s/yourdomain.com/$DOMAIN/g" .env.production

# Update CORS configuration
sed -i "s/yourdomain.com/$DOMAIN/g" park-and-rent-api/config/cors.php

echo "🏗️ Building frontend..."
npm install
cp .env.production .env
npm run build

echo "📦 Preparing backend..."
cd park-and-rent-api
composer install --optimize-autoloader --no-dev
php artisan key:generate
cd ..

echo "📁 Creating deployment packages..."

# Create frontend package
cd dist
zip -r ../frontend-deployment.zip .
cd ..

# Create backend package
cd park-and-rent-api
zip -r ../backend-deployment.zip . -x "node_modules/*" "vendor/*" ".git/*"
cd ..

echo "✅ Deployment packages created!"
echo ""
echo "📦 Files ready for upload:"
echo "   - frontend-deployment.zip (upload to public_html/)"
echo "   - backend-deployment.zip (upload to public_html/api/)"
echo "   - server-setup.php (upload to public_html/api/)"
echo ""
echo "🌐 Your site will be available at: https://$DOMAIN"
echo "🔧 Admin panel: https://$DOMAIN/admin"
echo "📡 API endpoint: https://$DOMAIN/api"
echo ""
echo "📋 Next steps:"
echo "1. Upload packages to Hostinger"
echo "2. Extract files in correct directories"
echo "3. Set up database in hPanel"
echo "4. Run server-setup.php"
echo "5. Test your application!"
echo ""
echo "📖 See DEPLOYMENT_GUIDE.md for detailed instructions"
