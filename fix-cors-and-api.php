<?php
// Fix CORS and API Setup
// Upload this to your public_html/api/ folder
// URL: https://ebisera.com/api/fix-cors-and-api.php

echo "<h2>🔧 Fix CORS and API Setup</h2>";

// 1. Create index.php with CORS headers
echo "<h3>1. Creating index.php with CORS support...</h3>";
$indexContent = '<?php

use Illuminate\Contracts\Http\Kernel;
use Illuminate\Http\Request;

define(\'LARAVEL_START\', microtime(true));

// Add CORS headers early
header(\'Access-Control-Allow-Origin: *\');
header(\'Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS\');
header(\'Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept\');

// Handle preflight OPTIONS requests
if ($_SERVER[\'REQUEST_METHOD\'] === \'OPTIONS\') {
    http_response_code(200);
    exit();
}

if (file_exists($maintenance = __DIR__.\'/storage/framework/maintenance.php\')) {
    require $maintenance;
}

require __DIR__.\'/vendor/autoload.php\';

$app = require_once __DIR__.\'/bootstrap/app.php\';

$kernel = $app->make(Kernel::class);

$response = $kernel->handle(
    $request = Request::capture()
)->send();

$kernel->terminate($request, $response);
';

file_put_contents('index.php', $indexContent);
echo "<p style='color: green;'>✅ index.php created with CORS support</p>";

// 2. Create enhanced .htaccess
echo "<h3>2. Creating enhanced .htaccess...</h3>";
$htaccessContent = '<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Handle preflight OPTIONS requests
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

# CORS Headers
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Accept, Origin"
    Header always set Access-Control-Max-Age "3600"
</IfModule>

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Disable directory browsing
Options -Indexes

# Protect sensitive files
<Files ".env">
    Order allow,deny
    Deny from all
</Files>';

file_put_contents('.htaccess', $htaccessContent);
echo "<p style='color: green;'>✅ .htaccess created with enhanced CORS</p>";

// 3. Update .env if it exists
echo "<h3>3. Checking .env configuration...</h3>";
if (file_exists('.env')) {
    $env = file_get_contents('.env');
    
    // Update APP_URL if it contains yourdomain.com
    if (strpos($env, 'yourdomain.com') !== false) {
        $env = str_replace('yourdomain.com', 'ebisera.com', $env);
        file_put_contents('.env', $env);
        echo "<p style='color: green;'>✅ Updated .env with correct domain</p>";
    } else {
        echo "<p style='color: green;'>✅ .env domain already correct</p>";
    }
    
    // Check if FRONTEND_URL is set
    if (strpos($env, 'FRONTEND_URL=') === false) {
        $env .= "\nFRONTEND_URL=https://ebisera.com\n";
        file_put_contents('.env', $env);
        echo "<p style='color: green;'>✅ Added FRONTEND_URL to .env</p>";
    }
} else {
    echo "<p style='color: red;'>❌ .env file not found</p>";
}

// 4. Set permissions
echo "<h3>4. Setting permissions...</h3>";
exec('chmod -R 755 storage 2>&1');
exec('chmod -R 755 bootstrap/cache 2>&1');
echo "<p style='color: green;'>✅ Permissions set</p>";

// 5. Laravel commands
echo "<h3>5. Running Laravel commands...</h3>";
exec('php artisan config:clear 2>&1');
exec('php artisan route:clear 2>&1');
exec('php artisan cache:clear 2>&1');
echo "<p style='color: green;'>✅ Caches cleared</p>";

exec('php artisan key:generate --force 2>&1');
echo "<p style='color: green;'>✅ App key generated</p>";

exec('php artisan migrate --force 2>&1', $migrateOutput);
if (empty($migrateOutput) || strpos(implode("\n", $migrateOutput), 'error') === false) {
    echo "<p style='color: green;'>✅ Migrations completed</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Migration output:</p>";
    echo "<pre>" . implode("\n", array_slice($migrateOutput, -5)) . "</pre>";
}

exec('php artisan db:seed --force 2>&1', $seedOutput);
echo "<p style='color: green;'>✅ Database seeding attempted</p>";

exec('php artisan storage:link 2>&1');
echo "<p style='color: green;'>✅ Storage link created</p>";

exec('php artisan config:cache 2>&1');
exec('php artisan route:cache 2>&1');
echo "<p style='color: green;'>✅ Production caches created</p>";

// 6. Test Laravel bootstrap
echo "<h3>6. Testing Laravel...</h3>";
try {
    if (file_exists('vendor/autoload.php') && file_exists('bootstrap/app.php')) {
        require_once 'vendor/autoload.php';
        $app = require_once 'bootstrap/app.php';
        echo "<p style='color: green;'>✅ Laravel bootstraps successfully</p>";
    } else {
        echo "<p style='color: red;'>❌ Laravel files missing</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Laravel error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>✅ Setup Complete!</h3>";
echo "<h4>🧪 Test these URLs now:</h4>";
echo "<ul>";
echo "<li><a href='https://ebisera.com/api/' target='_blank'>API Root</a></li>";
echo "<li><a href='https://ebisera.com/api/cars' target='_blank'>Cars API</a></li>";
echo "<li><a href='https://ebisera.com/api/drivers' target='_blank'>Drivers API</a></li>";
echo "</ul>";

echo "<h4>🔧 If still not working, try:</h4>";
echo "<ul>";
echo "<li><a href='https://ebisera.com/api/cars.php' target='_blank'>Mock Cars API</a></li>";
echo "<li><a href='https://ebisera.com/api/drivers.php' target='_blank'>Mock Drivers API</a></li>";
echo "</ul>";

echo "<p><strong>⚠️ Delete this file after testing!</strong></p>";
?>
