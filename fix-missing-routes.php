<?php
// Fix Missing API Routes
// Upload this to your public_html/api/ folder
// URL: https://ebisera.com/api/fix-missing-routes.php

echo "<h2>🔧 Fix Missing API Routes</h2>";

if (!file_exists('artisan')) {
    die("<p style='color: red;'>❌ Not in Laravel directory</p>");
}

echo "<h3>1. Checking Laravel setup...</h3>";
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    echo "<p style='color: green;'>✅ <PERSON><PERSON> bootstrapped successfully</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Laravel error: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h3>2. Checking route files...</h3>";
if (file_exists('routes/api.php')) {
    echo "<p style='color: green;'>✅ routes/api.php exists</p>";
    $apiRoutes = file_get_contents('routes/api.php');
    if (strpos($apiRoutes, '/cars') !== false) {
        echo "<p style='color: green;'>✅ Cars routes found in api.php</p>";
    } else {
        echo "<p style='color: red;'>❌ Cars routes not found in api.php</p>";
    }
} else {
    echo "<p style='color: red;'>❌ routes/api.php missing</p>";
}

echo "<h3>3. Checking controllers...</h3>";
if (file_exists('app/Http/Controllers/API/CarController.php')) {
    echo "<p style='color: green;'>✅ CarController exists</p>";
} else {
    echo "<p style='color: red;'>❌ CarController missing</p>";
}

if (file_exists('app/Http/Controllers/API/DriverController.php')) {
    echo "<p style='color: green;'>✅ DriverController exists</p>";
} else {
    echo "<p style='color: red;'>❌ DriverController missing</p>";
}

echo "<h3>4. Clearing all caches...</h3>";
exec('php artisan config:clear 2>&1', $configClear);
exec('php artisan route:clear 2>&1', $routeClear);
exec('php artisan cache:clear 2>&1', $cacheClear);
exec('php artisan view:clear 2>&1', $viewClear);
echo "<p style='color: green;'>✅ All caches cleared</p>";

echo "<h3>5. Checking current routes...</h3>";
exec('php artisan route:list 2>&1', $routeList, $routeReturn);
if ($routeReturn === 0) {
    $routeOutput = implode("\n", $routeList);
    if (strpos($routeOutput, 'api/cars') !== false) {
        echo "<p style='color: green;'>✅ API cars route is registered</p>";
    } else {
        echo "<p style='color: red;'>❌ API cars route not found</p>";
        echo "<p>Available routes:</p>";
        echo "<pre>" . implode("\n", array_slice($routeList, 0, 20)) . "</pre>";
    }
} else {
    echo "<p style='color: red;'>❌ Could not list routes</p>";
    echo "<pre>" . implode("\n", $routeList) . "</pre>";
}

echo "<h3>6. Checking database connection...</h3>";
exec('php artisan migrate:status 2>&1', $migrateStatus, $migrateReturn);
if ($migrateReturn === 0) {
    echo "<p style='color: green;'>✅ Database connected</p>";
    if (strpos(implode("\n", $migrateStatus), 'cars') !== false) {
        echo "<p style='color: green;'>✅ Cars table exists</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Cars table might not exist</p>";
        echo "<h4>Running migrations...</h4>";
        exec('php artisan migrate --force 2>&1', $migrateOutput);
        echo "<pre>" . implode("\n", array_slice($migrateOutput, -10)) . "</pre>";
    }
} else {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
    echo "<pre>" . implode("\n", $migrateStatus) . "</pre>";
}

echo "<h3>7. Seeding database...</h3>";
exec('php artisan db:seed --force 2>&1', $seedOutput, $seedReturn);
if ($seedReturn === 0) {
    echo "<p style='color: green;'>✅ Database seeded successfully</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Seeding output:</p>";
    echo "<pre>" . implode("\n", array_slice($seedOutput, -10)) . "</pre>";
}

echo "<h3>8. Caching routes for production...</h3>";
exec('php artisan route:cache 2>&1', $routeCache);
echo "<p style='color: green;'>✅ Routes cached</p>";

echo "<h3>9. Final route check...</h3>";
exec('php artisan route:list --path=api 2>&1', $finalRoutes);
echo "<p>API routes:</p>";
echo "<pre>" . implode("\n", array_slice($finalRoutes, 0, 15)) . "</pre>";

echo "<h3>10. Testing routes directly...</h3>";
try {
    // Test cars route
    $carsUrl = 'https://ebisera.com/api/cars';
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'ignore_errors' => true,
            'header' => "Accept: application/json\r\n"
        ]
    ]);
    
    $carsResponse = @file_get_contents($carsUrl, false, $context);
    if ($carsResponse !== false) {
        echo "<p style='color: green;'>✅ Cars API responding</p>";
        echo "<p>Response length: " . strlen($carsResponse) . " bytes</p>";
    } else {
        echo "<p style='color: red;'>❌ Cars API still not responding</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error testing routes: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>✅ Route Fix Complete!</h3>";
echo "<p>Test these URLs now:</p>";
echo "<ul>";
echo "<li><a href='https://ebisera.com/api/cars' target='_blank'>Cars API</a></li>";
echo "<li><a href='https://ebisera.com/api/drivers' target='_blank'>Drivers API</a></li>";
echo "</ul>";

echo "<h4>🔧 If still not working:</h4>";
echo "<ol>";
echo "<li>Check if .env file has correct database credentials</li>";
echo "<li>Verify tables exist in database</li>";
echo "<li>Check file permissions</li>";
echo "<li>Try refreshing the page after 30 seconds</li>";
echo "</ol>";

echo "<p><strong>⚠️ Delete this file after testing!</strong></p>";
?>
