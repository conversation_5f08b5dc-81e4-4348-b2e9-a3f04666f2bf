<?php
// Create Missing <PERSON>vel Files Script
// Upload this to your public_html/api/ folder and run it
// URL: https://ebisera.com/api/create-missing-files.php

echo "<h2>🔧 Creating Missing Laravel Files</h2>";

$currentDir = getcwd();
echo "<p>Working in directory: $currentDir</p>";

// Check if we're in the right place
if (!file_exists('artisan')) {
    die("<p style='color: red;'>❌ Error: artisan file not found. Make sure you're in the Laravel root directory.</p>");
}

echo "<h3>📁 Current files in directory:</h3>";
$files = scandir('.');
echo "<ul>";
foreach ($files as $file) {
    if ($file != '.' && $file != '..') {
        $type = is_dir($file) ? '[DIR]' : '[FILE]';
        echo "<li>$type $file</li>";
    }
}
echo "</ul>";

// 1. Create index.php
echo "<h3>1. 🔧 Creating index.php...</h3>";
$indexContent = '<?php

use Illuminate\Contracts\Http\Kernel;
use Illuminate\Http\Request;

define(\'LARAVEL_START\', microtime(true));

/*
|--------------------------------------------------------------------------
| Check If The Application Is Under Maintenance
|--------------------------------------------------------------------------
*/

if (file_exists($maintenance = __DIR__.\'/storage/framework/maintenance.php\')) {
    require $maintenance;
}

/*
|--------------------------------------------------------------------------
| Register The Auto Loader
|--------------------------------------------------------------------------
*/

require __DIR__.\'/vendor/autoload.php\';

/*
|--------------------------------------------------------------------------
| Run The Application
|--------------------------------------------------------------------------
*/

$app = require_once __DIR__.\'/bootstrap/app.php\';

$kernel = $app->make(Kernel::class);

$response = $kernel->handle(
    $request = Request::capture()
)->send();

$kernel->terminate($request, $response);
';

if (file_put_contents('index.php', $indexContent)) {
    echo "<p style='color: green;'>✅ index.php created successfully</p>";
} else {
    echo "<p style='color: red;'>❌ Failed to create index.php</p>";
}

// 2. Create .htaccess
echo "<h3>2. 🔧 Creating .htaccess...</h3>";
$htaccessContent = '<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

# CORS Headers for API
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Accept"
</IfModule>

# Disable directory browsing
Options -Indexes

# Protect sensitive files
<Files ".env">
    Order allow,deny
    Deny from all
</Files>';

if (file_put_contents('.htaccess', $htaccessContent)) {
    echo "<p style='color: green;'>✅ .htaccess created successfully</p>";
} else {
    echo "<p style='color: red;'>❌ Failed to create .htaccess</p>";
}

// 3. Set permissions
echo "<h3>3. 🔒 Setting permissions...</h3>";
if (is_dir('storage')) {
    chmod('storage', 0755);
    exec('chmod -R 755 storage');
    echo "<p style='color: green;'>✅ Storage permissions set</p>";
} else {
    echo "<p style='color: red;'>❌ Storage directory not found</p>";
}

if (is_dir('bootstrap/cache')) {
    chmod('bootstrap/cache', 0755);
    exec('chmod -R 755 bootstrap/cache');
    echo "<p style='color: green;'>✅ Bootstrap cache permissions set</p>";
} else {
    echo "<p style='color: red;'>❌ Bootstrap cache directory not found</p>";
}

// 4. Clear caches
echo "<h3>4. 🧹 Clearing caches...</h3>";
exec('php artisan config:clear 2>&1', $output1);
exec('php artisan route:clear 2>&1', $output2);
exec('php artisan view:clear 2>&1', $output3);
exec('php artisan cache:clear 2>&1', $output4);
echo "<p style='color: green;'>✅ All caches cleared</p>";

// 5. Generate app key if needed
echo "<h3>5. 🔑 Checking app key...</h3>";
if (file_exists('.env')) {
    $env = file_get_contents('.env');
    if (strpos($env, 'APP_KEY=') === false || strpos($env, 'APP_KEY=base64:') === false) {
        exec('php artisan key:generate --force 2>&1', $output5);
        echo "<p style='color: green;'>✅ App key generated</p>";
    } else {
        echo "<p style='color: green;'>✅ App key already exists</p>";
    }
} else {
    echo "<p style='color: red;'>❌ .env file not found</p>";
}

// 6. Run migrations
echo "<h3>6. 🗄️ Running migrations...</h3>";
exec('php artisan migrate --force 2>&1', $migrateOutput, $migrateReturn);
if ($migrateReturn === 0) {
    echo "<p style='color: green;'>✅ Migrations completed successfully</p>";
} else {
    echo "<p style='color: red;'>❌ Migration failed:</p>";
    echo "<pre>" . implode("\n", $migrateOutput) . "</pre>";
}

// 7. Seed database
echo "<h3>7. 🌱 Seeding database...</h3>";
exec('php artisan db:seed --force 2>&1', $seedOutput, $seedReturn);
if ($seedReturn === 0) {
    echo "<p style='color: green;'>✅ Database seeded successfully</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Seeding result (may be normal if already seeded):</p>";
    echo "<pre>" . implode("\n", array_slice($seedOutput, -10)) . "</pre>";
}

// 8. Create storage link
echo "<h3>8. 🔗 Creating storage link...</h3>";
exec('php artisan storage:link 2>&1', $linkOutput);
echo "<p style='color: green;'>✅ Storage link created</p>";

// 9. Cache for production
echo "<h3>9. ⚡ Caching for production...</h3>";
exec('php artisan config:cache 2>&1', $configCache);
exec('php artisan route:cache 2>&1', $routeCache);
echo "<p style='color: green;'>✅ Production caches created</p>";

// 10. Test the setup
echo "<h3>10. 🧪 Testing setup...</h3>";

// Test if Laravel loads
try {
    if (file_exists('vendor/autoload.php') && file_exists('bootstrap/app.php')) {
        require_once 'vendor/autoload.php';
        $app = require_once 'bootstrap/app.php';
        echo "<p style='color: green;'>✅ Laravel application loads successfully</p>";
    } else {
        echo "<p style='color: red;'>❌ Missing vendor/autoload.php or bootstrap/app.php</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Laravel failed to load: " . $e->getMessage() . "</p>";
}

// Test routes
exec('php artisan route:list 2>&1', $routeList);
$routeOutput = implode("\n", $routeList);
if (strpos($routeOutput, 'api/cars') !== false) {
    echo "<p style='color: green;'>✅ API routes are registered</p>";
} else {
    echo "<p style='color: red;'>❌ API routes not found</p>";
}

echo "<hr>";
echo "<h3>✅ Setup Complete!</h3>";

echo "<h4>📋 Files created:</h4>";
echo "<ul>";
if (file_exists('index.php')) echo "<li style='color: green;'>✅ index.php</li>";
if (file_exists('.htaccess')) echo "<li style='color: green;'>✅ .htaccess</li>";
echo "</ul>";

echo "<h4>🧪 Test these URLs now:</h4>";
echo "<ul>";
echo "<li><a href='https://ebisera.com/api/' target='_blank'>https://ebisera.com/api/</a> (should show Laravel info)</li>";
echo "<li><a href='https://ebisera.com/api/cars' target='_blank'>https://ebisera.com/api/cars</a> (should return JSON)</li>";
echo "<li><a href='https://ebisera.com/api/drivers' target='_blank'>https://ebisera.com/api/drivers</a> (should return JSON)</li>";
echo "</ul>";

echo "<h4>🎯 Next steps:</h4>";
echo "<ol>";
echo "<li>Test the API URLs above</li>";
echo "<li>Check your frontend at <a href='https://ebisera.com' target='_blank'>https://ebisera.com</a></li>";
echo "<li>Delete this file for security</li>";
echo "</ol>";

echo "<p><strong>⚠️ IMPORTANT: Delete this file after testing!</strong></p>";
?>
