<?php
// Fix API Routes
// Upload this to your public_html/api/ folder
// URL: https://ebisera.com/api/fix-api-routes.php

echo "<h2>🔧 Fix API Routes</h2>";

// Check if <PERSON><PERSON> is working
if (!file_exists('artisan')) {
    die("<p style='color: red;'>❌ Not in Laravel directory</p>");
}

echo "<h3>1. Testing Laravel...</h3>";
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    echo "<p style='color: green;'>✅ Laravel is working</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Laravel error: " . $e->getMessage() . "</p>";
}

echo "<h3>2. Checking routes...</h3>";
exec('php artisan route:list --path=api 2>&1', $routeOutput, $routeReturn);
if ($routeReturn === 0) {
    echo "<p style='color: green;'>✅ API routes found:</p>";
    echo "<pre>" . implode("\n", array_slice($routeOutput, 0, 15)) . "</pre>";
} else {
    echo "<p style='color: red;'>❌ Route listing failed</p>";
    echo "<pre>" . implode("\n", $routeOutput) . "</pre>";
}

echo "<h3>3. Clearing caches...</h3>";
exec('php artisan config:clear 2>&1');
exec('php artisan route:clear 2>&1');
exec('php artisan cache:clear 2>&1');
echo "<p style='color: green;'>✅ Caches cleared</p>";

echo "<h3>4. Caching routes...</h3>";
exec('php artisan route:cache 2>&1');
echo "<p style='color: green;'>✅ Routes cached</p>";

echo "<h3>5. Testing database...</h3>";
exec('php artisan migrate:status 2>&1', $migrateStatus, $migrateReturn);
if ($migrateReturn === 0) {
    echo "<p style='color: green;'>✅ Database connected</p>";
} else {
    echo "<p style='color: red;'>❌ Database issue:</p>";
    echo "<pre>" . implode("\n", array_slice($migrateStatus, -5)) . "</pre>";
    
    echo "<h4>Running migrations...</h4>";
    exec('php artisan migrate --force 2>&1', $migrateOutput);
    echo "<pre>" . implode("\n", array_slice($migrateOutput, -5)) . "</pre>";
}

echo "<h3>6. Seeding database...</h3>";
exec('php artisan db:seed --force 2>&1', $seedOutput);
echo "<p style='color: green;'>✅ Database seeding attempted</p>";

echo "<hr>";
echo "<h3>✅ Routes Fixed!</h3>";
echo "<p>Test these URLs:</p>";
echo "<ul>";
echo "<li><a href='https://ebisera.com/api/cars' target='_blank'>Cars API</a></li>";
echo "<li><a href='https://ebisera.com/api/drivers' target='_blank'>Drivers API</a></li>";
echo "</ul>";
echo "<p><strong>Delete this file after testing!</strong></p>";
?>
