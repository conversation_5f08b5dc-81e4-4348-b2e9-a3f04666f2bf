<?php
// Direct Laravel Test
// Upload this to your public_html/api/ folder
// URL: https://ebisera.com/api/direct-test.php

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

echo json_encode([
    'status' => 'success',
    'message' => 'Direct PHP test working',
    'timestamp' => date('Y-m-d H:i:s'),
    'server_info' => [
        'php_version' => phpversion(),
        'current_dir' => getcwd(),
        'laravel_files' => [
            'artisan' => file_exists('artisan'),
            'vendor' => is_dir('vendor'),
            'bootstrap' => file_exists('bootstrap/app.php'),
            'env' => file_exists('.env'),
            'index' => file_exists('index.php'),
            'htaccess' => file_exists('.htaccess')
        ]
    ]
]);
?>
