<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .loading { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🔍 Frontend API Connection Test</h1>
    
    <h2>Testing API Endpoints:</h2>
    <div id="results"></div>
    
    <script>
        const resultsDiv = document.getElementById('results');
        
        async function testAPI() {
            resultsDiv.innerHTML = '<p class="loading">🔄 Testing API connections...</p>';
            
            const tests = [
                { name: 'Cars API', url: 'https://ebisera.com/api/cars' },
                { name: 'Drivers API', url: 'https://ebisera.com/api/drivers' },
                { name: 'API Root', url: 'https://ebisera.com/api/' }
            ];
            
            let results = '';
            
            for (const test of tests) {
                try {
                    results += `<h3>Testing ${test.name}:</h3>`;
                    
                    const response = await fetch(test.url, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.text();
                        results += `<p class="success">✅ ${test.name} - Status: ${response.status}</p>`;
                        results += `<p>Response length: ${data.length} characters</p>`;
                        
                        // Try to parse as JSON
                        try {
                            const jsonData = JSON.parse(data);
                            if (Array.isArray(jsonData)) {
                                results += `<p>✅ Valid JSON array with ${jsonData.length} items</p>`;
                            } else {
                                results += `<p>✅ Valid JSON object</p>`;
                            }
                        } catch (e) {
                            results += `<p>⚠️ Response is not JSON (might be HTML)</p>`;
                        }
                        
                        results += `<details><summary>Show response</summary><pre>${data.substring(0, 500)}${data.length > 500 ? '...' : ''}</pre></details>`;
                    } else {
                        results += `<p class="error">❌ ${test.name} - Status: ${response.status}</p>`;
                        const errorText = await response.text();
                        results += `<pre>${errorText.substring(0, 300)}</pre>`;
                    }
                } catch (error) {
                    results += `<p class="error">❌ ${test.name} - Error: ${error.message}</p>`;
                }
                
                results += '<hr>';
            }
            
            resultsDiv.innerHTML = results;
        }
        
        // Run tests when page loads
        testAPI();
    </script>
</body>
</html>
