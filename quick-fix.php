<?php
// Quick Fix Script for Common Deployment Issues
// Upload this to your public_html/api/ folder
// URL: https://yourdomain.com/api/quick-fix.php

echo "<h2>🔧 Quick Fix Script</h2>";

if (!file_exists('artisan')) {
    die("<p style='color: red;'>❌ Laravel not found. Make sure you're in the correct directory.</p>");
}

echo "<h3>🔄 Running Fixes...</h3>";

// Fix 1: Set proper permissions
echo "<h4>1. Setting Permissions:</h4>";
exec('chmod -R 755 storage 2>&1', $output1);
exec('chmod -R 755 bootstrap/cache 2>&1', $output2);
echo "<p style='color: green;'>✅ Permissions set for storage and bootstrap/cache</p>";

// Fix 2: Clear all caches
echo "<h4>2. Clearing Caches:</h4>";
exec('php artisan config:clear 2>&1', $output3);
exec('php artisan route:clear 2>&1', $output4);
exec('php artisan view:clear 2>&1', $output5);
exec('php artisan cache:clear 2>&1', $output6);
echo "<p style='color: green;'>✅ All caches cleared</p>";

// Fix 3: Generate app key if missing
echo "<h4>3. Checking App Key:</h4>";
if (file_exists('.env')) {
    $env = file_get_contents('.env');
    if (strpos($env, 'APP_KEY=base64:') === false || strpos($env, 'APP_KEY=') === false) {
        exec('php artisan key:generate --force 2>&1', $output7);
        echo "<p style='color: green;'>✅ App key generated</p>";
    } else {
        echo "<p style='color: green;'>✅ App key already exists</p>";
    }
} else {
    echo "<p style='color: red;'>❌ .env file not found</p>";
}

// Fix 4: Run migrations
echo "<h4>4. Running Migrations:</h4>";
exec('php artisan migrate --force 2>&1', $output8, $return8);
if ($return8 === 0) {
    echo "<p style='color: green;'>✅ Migrations completed</p>";
} else {
    echo "<p style='color: red;'>❌ Migration failed:</p>";
    echo "<pre>" . implode("\n", $output8) . "</pre>";
}

// Fix 5: Seed database
echo "<h4>5. Seeding Database:</h4>";
exec('php artisan db:seed --force 2>&1', $output9, $return9);
if ($return9 === 0) {
    echo "<p style='color: green;'>✅ Database seeded</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Seeding may have failed (this is normal if already seeded):</p>";
    echo "<pre>" . implode("\n", $output9) . "</pre>";
}

// Fix 6: Create storage link
echo "<h4>6. Creating Storage Link:</h4>";
exec('php artisan storage:link 2>&1', $output10, $return10);
if ($return10 === 0) {
    echo "<p style='color: green;'>✅ Storage link created</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Storage link may already exist:</p>";
    echo "<pre>" . implode("\n", $output10) . "</pre>";
}

// Fix 7: Cache config for production
echo "<h4>7. Caching Configuration:</h4>";
exec('php artisan config:cache 2>&1', $output11);
exec('php artisan route:cache 2>&1', $output12);
echo "<p style='color: green;'>✅ Configuration cached for production</p>";

// Test API endpoint
echo "<h4>8. Testing API:</h4>";
if (file_exists('.env')) {
    $env = file_get_contents('.env');
    if (preg_match('/APP_URL=(.+)/', $env, $matches)) {
        $app_url = trim($matches[1]);
        $api_test_url = $app_url . '/api/cars';
        echo "<p>Testing API endpoint: <a href='$api_test_url' target='_blank'>$api_test_url</a></p>";
        
        // Try to make a request to the API
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'ignore_errors' => true
            ]
        ]);
        
        $response = @file_get_contents($api_test_url, false, $context);
        if ($response !== false) {
            echo "<p style='color: green;'>✅ API endpoint is responding</p>";
        } else {
            echo "<p style='color: red;'>❌ API endpoint not responding</p>";
        }
    }
}

echo "<hr>";
echo "<h3>✅ Quick Fix Complete!</h3>";
echo "<p>If you're still having issues, check the database connection using db-test.php</p>";
echo "<p><strong>⚠️ IMPORTANT: Delete this file after use for security!</strong></p>";
?>
