import axios from 'axios';

// API Configuration
export const API_BASE_URL = '/api';

// Create axios instance with default configuration
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000, // 10 seconds timeout
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add request logging for debugging (simplified)
apiClient.interceptors.request.use(
  (config) => {
    // Only log in development mode
    if (import.meta.env.DEV) {
      console.log('API Request:', config.method?.toUpperCase(), config.baseURL + config.url);
    }
    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors
apiClient.interceptors.response.use(
  (response) => {
    // Only log in development mode
    if (import.meta.env.DEV) {
      console.log('API Response:', response.status, `${Array.isArray(response.data) ? response.data.length + ' items' : 'data'}`);
    }
    return response;
  },
  (error) => {
    console.error('API error:', error.message);

    // Handle 401 Unauthorized errors
    if (error.response?.status === 401) {
      // Clear auth data and redirect to login
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }

    return Promise.reject(error);
  }
);

// API endpoints
export const API_ENDPOINTS = {
  // Auth endpoints
  REGISTER: '/register',
  LOGIN: '/login',
  LOGOUT: '/logout',
  USER: '/user',

  // User endpoints
  USERS: '/users',
  UPDATE_PASSWORD: '/users/update-password',

  // Car endpoints
  CARS: '/cars',
  MY_CARS: '/my-cars',

  // Driver endpoints
  DRIVERS: '/drivers',
  MY_DRIVER_PROFILE: '/my-driver-profile',

  // Booking endpoints
  BOOKINGS: '/bookings',
  MY_BOOKINGS: '/my-bookings',

  // Chat endpoints
  CHATS: '/chats',
  MY_CHATS: '/my-chats',
  UNREAD_COUNT: '/unread-messages-count',

  // Message endpoints
  MESSAGES: '/messages',
  MARK_AS_READ: '/messages/mark-as-read',

  // Admin endpoints
  ADMIN: {
    DASHBOARD: '/admin/dashboard',
    USERS: '/admin/users',
    CARS: '/admin/cars',
    DRIVERS: '/admin/drivers',
    BOOKINGS: '/admin/bookings',
    REVENUE: '/admin/revenue',
  },
};
