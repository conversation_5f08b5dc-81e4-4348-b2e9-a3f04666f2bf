<?php
// API Structure Fix Script
// Upload this to your public_html/api/ folder and run it
// URL: https://ebisera.com/api/fix-api-structure.php

echo "<h2>🔧 Fixing API Structure</h2>";

$currentDir = getcwd();
echo "<p>Current directory: $currentDir</p>";

// Check if we're in the right place
if (!file_exists('artisan')) {
    die("<p style='color: red;'>❌ Error: artisan file not found. Make sure you're in the Laravel root directory.</p>");
}

echo "<h3>📁 Current File Structure:</h3>";
$files = scandir('.');
echo "<ul>";
foreach ($files as $file) {
    if ($file != '.' && $file != '..') {
        $type = is_dir($file) ? '[DIR]' : '[FILE]';
        echo "<li>$type $file</li>";
    }
}
echo "</ul>";

// Check if public directory exists
if (is_dir('public')) {
    echo "<h3>🔄 Moving Laravel public files to API root...</h3>";
    
    // Move index.php from public to api root
    if (file_exists('public/index.php')) {
        if (copy('public/index.php', 'index.php')) {
            echo "<p style='color: green;'>✅ Copied index.php to API root</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to copy index.php</p>";
        }
    }
    
    // Move .htaccess from public to api root
    if (file_exists('public/.htaccess')) {
        if (copy('public/.htaccess', '.htaccess')) {
            echo "<p style='color: green;'>✅ Copied .htaccess to API root</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to copy .htaccess</p>";
        }
    }
    
    // Update the index.php to point to correct paths
    if (file_exists('index.php')) {
        $indexContent = file_get_contents('index.php');
        
        // Update the autoload path
        $indexContent = str_replace(
            "require __DIR__.'/../vendor/autoload.php';",
            "require __DIR__.'/vendor/autoload.php';",
            $indexContent
        );
        
        // Update the bootstrap path
        $indexContent = str_replace(
            "require_once __DIR__.'/../bootstrap/app.php';",
            "require_once __DIR__.'/bootstrap/app.php';",
            $indexContent
        );
        
        if (file_put_contents('index.php', $indexContent)) {
            echo "<p style='color: green;'>✅ Updated index.php paths</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to update index.php</p>";
        }
    }
    
} else {
    echo "<p style='color: orange;'>⚠️ Public directory not found. Creating index.php manually...</p>";
    
    // Create index.php manually
    $indexContent = '<?php

use Illuminate\Contracts\Http\Kernel;
use Illuminate\Http\Request;

define(\'LARAVEL_START\', microtime(true));

/*
|--------------------------------------------------------------------------
| Check If The Application Is Under Maintenance
|--------------------------------------------------------------------------
|
| If the application is in maintenance / demo mode via the "down" command
| we will load this file so that any pre-rendered content can be shown
| instead of starting the framework, which could cause an exception.
|
*/

if (file_exists($maintenance = __DIR__.\'/../storage/framework/maintenance.php\')) {
    require $maintenance;
}

/*
|--------------------------------------------------------------------------
| Register The Auto Loader
|--------------------------------------------------------------------------
|
| Composer provides a convenient, automatically generated class loader for
| this application. We just need to utilize it! We\'ll simply require it
| into the script here so we don\'t need to manually load our classes.
|
*/

require __DIR__.\'/vendor/autoload.php\';

/*
|--------------------------------------------------------------------------
| Run The Application
|--------------------------------------------------------------------------
|
| Once we have the application, we can handle the incoming request using
| the application\'s HTTP kernel. Then, we will send the response back
| to this client\'s browser, allowing them to enjoy our application.
|
*/

$app = require_once __DIR__.\'/bootstrap/app.php\';

$kernel = $app->make(Kernel::class);

$response = $kernel->handle(
    $request = Request::capture()
)->send();

$kernel->terminate($request, $response);
';
    
    if (file_put_contents('index.php', $indexContent)) {
        echo "<p style='color: green;'>✅ Created index.php</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create index.php</p>";
    }
}

// Create/update .htaccess for API
$htaccessContent = '<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# CORS Headers
<IfModule mod_headers.c>
    Header add Access-Control-Allow-Origin "*"
    Header add Access-Control-Allow-Headers "origin, x-requested-with, content-type, authorization"
    Header add Access-Control-Allow-Methods "PUT, GET, POST, DELETE, OPTIONS"
</IfModule>

# Disable directory browsing
Options -Indexes

# Protect sensitive files
<Files ".env">
    Order allow,deny
    Deny from all
</Files>';

if (file_put_contents('.htaccess', $htaccessContent)) {
    echo "<p style='color: green;'>✅ Created/updated .htaccess</p>";
} else {
    echo "<p style='color: red;'>❌ Failed to create .htaccess</p>";
}

// Set permissions
echo "<h3>🔒 Setting Permissions:</h3>";
chmod('storage', 0755);
chmod('bootstrap/cache', 0755);
if (file_exists('.env')) {
    chmod('.env', 0644);
}
echo "<p style='color: green;'>✅ Permissions set</p>";

// Test the API
echo "<h3>🧪 Testing API:</h3>";
$testUrl = 'https://ebisera.com/api/';
echo "<p>Testing: <a href='$testUrl' target='_blank'>$testUrl</a></p>";

echo "<hr>";
echo "<h3>✅ Structure Fix Complete!</h3>";
echo "<p>Now test your API endpoints:</p>";
echo "<ul>";
echo "<li><a href='https://ebisera.com/api/cars' target='_blank'>https://ebisera.com/api/cars</a></li>";
echo "<li><a href='https://ebisera.com/api/drivers' target='_blank'>https://ebisera.com/api/drivers</a></li>";
echo "</ul>";
echo "<p><strong>⚠️ Delete this file after use!</strong></p>";
?>
