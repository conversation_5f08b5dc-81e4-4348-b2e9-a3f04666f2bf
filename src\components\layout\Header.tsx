import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Menu, X, Car, User, LogOut, Home, UserRound } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import Button from '../ui/Button';

const Header: React.FC = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleLogout = () => {
    logout();
    navigate('/');
    setIsMenuOpen(false);
  };

  const navLinks = [
    { name: 'Home', path: '/', icon: <Home size={18} /> },
    { name: 'Browse Cars', path: '/cars', icon: <Car size={18} /> },
    { name: 'Hire a Driver', path: '/drivers', icon: <UserRound size={18} /> },
  ];

  const getDashboardLink = () => {
    if (!user) return { name: 'My Account', path: '/account', icon: <User size={18} /> };

    switch (user.role) {
      case 'admin':
        return { name: 'Admin Dashboard', path: '/admin', icon: <User size={18} /> };
      case 'owner':
        return { name: 'Owner Dashboard', path: '/owner/dashboard', icon: <Car size={18} /> };
      case 'driver':
        return { name: 'Driver Dashboard', path: '/driver/dashboard', icon: <UserRound size={18} /> };
      case 'client':
        return { name: 'My Account', path: '/account', icon: <User size={18} /> };
      default:
        return { name: 'My Account', path: '/account', icon: <User size={18} /> };
    }
  };

  // Check if user is admin
  const isAdmin = user?.role === 'admin';

  const authLinks = isAuthenticated
    ? [getDashboardLink()]
    : [
        { name: 'Log In', path: '/login', icon: <User size={18} /> },
        { name: 'Sign Up', path: '/signup', icon: <User size={18} /> },
      ];

  return (
    <header className="bg-white shadow-md sticky top-0 z-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <Car className="h-8 w-8 text-primary-600" />
            <span className="text-xl font-bold text-gray-900">Park & Rent</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-1">
            {navLinks.map((link) => (
              <Link
                key={link.path}
                to={link.path}
                className="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 transition-colors"
              >
                {link.name}
              </Link>
            ))}

            {isAuthenticated ? (
              <>
                <Link
                  to={getDashboardLink().path}
                  className="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 transition-colors"
                >
                  {getDashboardLink().name}
                </Link>

                {isAdmin && (
                  <Link
                    to="/admin/dashboard"
                    className="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 transition-colors"
                  >
                    Admin Dashboard
                  </Link>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLogout}
                  className="ml-2"
                >
                  <LogOut size={16} className="mr-1" />
                  Log Out
                </Button>
              </>
            ) : (
              <>
                <Link to="/login">
                  <Button variant="outline" size="sm" className="ml-2">
                    Log In
                  </Button>
                </Link>
                <Link to="/signup">
                  <Button size="sm" className="ml-2">
                    Sign Up
                  </Button>
                </Link>
              </>
            )}
          </nav>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden rounded-md p-2 text-gray-700 hover:bg-gray-100 focus:outline-none"
            onClick={toggleMenu}
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden animate-fade-in">
          <div className="px-2 pt-2 pb-4 space-y-1 sm:px-3 border-t border-gray-200">
            {navLinks.map((link) => (
              <Link
                key={link.path}
                to={link.path}
                className="flex items-center px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600"
                onClick={() => setIsMenuOpen(false)}
              >
                {link.icon}
                <span className="ml-2">{link.name}</span>
              </Link>
            ))}

            {authLinks.map((link) => (
              <Link
                key={link.path}
                to={link.path}
                className="flex items-center px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600"
                onClick={() => setIsMenuOpen(false)}
              >
                {link.icon}
                <span className="ml-2">{link.name}</span>
              </Link>
            ))}

            {isAuthenticated && (
              <button
                className="flex items-center w-full px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600"
                onClick={handleLogout}
              >
                <LogOut size={18} />
                <span className="ml-2">Log Out</span>
              </button>
            )}
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;