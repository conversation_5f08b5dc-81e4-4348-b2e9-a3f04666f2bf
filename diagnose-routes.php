<?php
// Deep Route Diagnosis
// Upload this to your public_html/api/ folder
// URL: https://ebisera.com/api/diagnose-routes.php

echo "<h2>🔍 Deep Route Diagnosis</h2>";

if (!file_exists('artisan')) {
    die("<p style='color: red;'>❌ Not in Laravel directory</p>");
}

echo "<h3>1. Environment Check:</h3>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Current Directory: " . getcwd() . "</p>";

echo "<h3>2. Laravel Files Check:</h3>";
$files = ['artisan', 'bootstrap/app.php', 'vendor/autoload.php', 'routes/api.php', '.env'];
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $file exists</p>";
    } else {
        echo "<p style='color: red;'>❌ $file missing</p>";
    }
}

echo "<h3>3. Bootstrap Test:</h3>";
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    echo "<p style='color: green;'>✅ Laravel app created</p>";
    
    // Check if app is in debug mode
    $config = $app->make('config');
    echo "<p>App Debug: " . ($config->get('app.debug') ? 'true' : 'false') . "</p>";
    echo "<p>App Environment: " . $config->get('app.env') . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Laravel bootstrap failed: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p><pre>" . $e->getTraceAsString() . "</pre>";
    exit;
}

echo "<h3>4. Route Provider Check:</h3>";
try {
    $providers = $app->getLoadedProviders();
    if (isset($providers['Illuminate\Routing\RoutingServiceProvider'])) {
        echo "<p style='color: green;'>✅ Routing service provider loaded</p>";
    } else {
        echo "<p style='color: red;'>❌ Routing service provider not loaded</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking providers: " . $e->getMessage() . "</p>";
}

echo "<h3>5. Routes File Content:</h3>";
if (file_exists('routes/api.php')) {
    $apiContent = file_get_contents('routes/api.php');
    echo "<p style='color: green;'>✅ routes/api.php content (first 1000 chars):</p>";
    echo "<pre>" . htmlspecialchars(substr($apiContent, 0, 1000)) . "</pre>";
} else {
    echo "<p style='color: red;'>❌ routes/api.php not found</p>";
}

echo "<h3>6. Manual Route Registration Test:</h3>";
try {
    // Try to manually register a test route
    $router = $app->make('router');
    
    // Add a test route
    $router->get('api/test-route', function() {
        return response()->json(['message' => 'Test route works!', 'timestamp' => now()]);
    });
    
    echo "<p style='color: green;'>✅ Test route registered manually</p>";
    echo "<p>Try: <a href='https://ebisera.com/api/test-route' target='_blank'>https://ebisera.com/api/test-route</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Manual route registration failed: " . $e->getMessage() . "</p>";
}

echo "<h3>7. Artisan Commands Test:</h3>";
$commands = [
    'route:clear' => 'Clear route cache',
    'config:clear' => 'Clear config cache',
    'cache:clear' => 'Clear application cache'
];

foreach ($commands as $command => $description) {
    exec("php artisan $command 2>&1", $output, $return);
    if ($return === 0) {
        echo "<p style='color: green;'>✅ $description completed</p>";
    } else {
        echo "<p style='color: red;'>❌ $description failed</p>";
        echo "<pre>" . implode("\n", $output) . "</pre>";
    }
    $output = []; // Reset for next command
}

echo "<h3>8. Route List Attempt:</h3>";
exec('php artisan route:list 2>&1', $routeOutput, $routeReturn);
if ($routeReturn === 0) {
    echo "<p style='color: green;'>✅ Route list generated:</p>";
    echo "<pre>" . implode("\n", array_slice($routeOutput, 0, 20)) . "</pre>";
} else {
    echo "<p style='color: red;'>❌ Route list failed:</p>";
    echo "<pre>" . implode("\n", $routeOutput) . "</pre>";
}

echo "<h3>9. Database Connection Test:</h3>";
try {
    $db = $app->make('db');
    $connection = $db->connection();
    $pdo = $connection->getPdo();
    echo "<p style='color: green;'>✅ Database connected</p>";
    
    // Check if cars table exists
    $tables = $connection->select("SHOW TABLES LIKE 'cars'");
    if (count($tables) > 0) {
        echo "<p style='color: green;'>✅ Cars table exists</p>";
        
        // Count cars
        $carCount = $connection->select("SELECT COUNT(*) as count FROM cars")[0]->count;
        echo "<p>Cars in database: $carCount</p>";
    } else {
        echo "<p style='color: red;'>❌ Cars table does not exist</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
}

echo "<h3>10. .htaccess Check:</h3>";
if (file_exists('.htaccess')) {
    echo "<p style='color: green;'>✅ .htaccess exists</p>";
    $htaccess = file_get_contents('.htaccess');
    echo "<p>.htaccess content (first 500 chars):</p>";
    echo "<pre>" . htmlspecialchars(substr($htaccess, 0, 500)) . "</pre>";
} else {
    echo "<p style='color: red;'>❌ .htaccess missing</p>";
}

echo "<h3>11. index.php Check:</h3>";
if (file_exists('index.php')) {
    echo "<p style='color: green;'>✅ index.php exists</p>";
    $indexContent = file_get_contents('index.php');
    echo "<p>index.php content (first 500 chars):</p>";
    echo "<pre>" . htmlspecialchars(substr($indexContent, 0, 500)) . "</pre>";
} else {
    echo "<p style='color: red;'>❌ index.php missing</p>";
}

echo "<hr>";
echo "<h3>🎯 Diagnosis Summary:</h3>";
echo "<p>This diagnostic will help identify why the API routes are not working.</p>";
echo "<p>Please share the results so we can determine the exact issue.</p>";
echo "<p><strong>⚠️ Delete this file after testing!</strong></p>";
?>
