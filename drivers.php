<?php
// Mock Drivers API Response
// Upload this to your public_html/api/ folder
// This will work as https://ebisera.com/api/drivers.php

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Mock drivers data
$drivers = [
    [
        'id' => '1',
        'userId' => '1',
        'name' => '<PERSON>',
        'age' => 32,
        'experience' => 8,
        'profileImage' => '',
        'licenseNumber' => 'DL123456',
        'licenseVerificationStatus' => 'verified',
        'location' => 'Kigali',
        'pricePerHour' => 15,
        'rating' => 4.8,
        'reviews' => 45,
        'specialties' => ['City Driving', 'Airport Transfers'],
        'availabilityNotes' => 'Available 24/7',
        'isAvailable' => true,
        'isActive' => true,
        'createdAt' => '2024-01-01T00:00:00.000Z'
    ],
    [
        'id' => '2',
        'userId' => '2',
        'name' => '<PERSON>',
        'age' => 28,
        'experience' => 5,
        'profileImage' => '',
        'licenseNumber' => 'DL789012',
        'licenseVerificationStatus' => 'verified',
        'location' => 'Kigali',
        'pricePerHour' => 18,
        'rating' => 4.9,
        'reviews' => 32,
        'specialties' => ['Long Distance', 'Tourism'],
        'availabilityNotes' => 'Available weekdays',
        'isAvailable' => true,
        'isActive' => true,
        'createdAt' => '2024-01-02T00:00:00.000Z'
    ],
    [
        'id' => '3',
        'userId' => '3',
        'name' => 'David Brown',
        'age' => 35,
        'experience' => 12,
        'profileImage' => '',
        'licenseNumber' => 'DL345678',
        'licenseVerificationStatus' => 'verified',
        'location' => 'Kigali',
        'pricePerHour' => 20,
        'rating' => 4.7,
        'reviews' => 67,
        'specialties' => ['Business Trips', 'VIP Service'],
        'availabilityNotes' => 'Available by appointment',
        'isAvailable' => true,
        'isActive' => true,
        'createdAt' => '2024-01-03T00:00:00.000Z'
    ]
];

echo json_encode($drivers);
?>
