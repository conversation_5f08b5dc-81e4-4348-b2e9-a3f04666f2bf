#!/bin/bash

echo "🚀 Preparing <PERSON><PERSON> backend for deployment..."

# Navigate to Laravel directory
cd park-and-rent-api

# Install dependencies (if not already done)
composer install --optimize-autoloader --no-dev

# Generate application key
php artisan key:generate

# Clear and cache config
php artisan config:clear
php artisan config:cache

# Clear and cache routes
php artisan route:clear
php artisan route:cache

# Clear and cache views
php artisan view:clear
php artisan view:cache

# Create symbolic link for storage
php artisan storage:link

echo "✅ Laravel backend prepared for deployment!"
echo "📁 Upload the entire 'park-and-rent-api' folder to your hosting 'public_html/api' directory"
echo "🔧 Don't forget to:"
echo "   1. Copy .env.production to .env on the server"
echo "   2. Update database credentials in .env"
echo "   3. Run migrations: php artisan migrate --force"
echo "   4. Seed database: php artisan db:seed --force"
