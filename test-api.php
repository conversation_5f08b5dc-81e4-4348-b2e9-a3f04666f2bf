<?php
// Simple API Test
// Upload this to your public_html/api/ folder
// URL: https://ebisera.com/api/test-api.php

echo "<h2>🔍 API Debug Test</h2>";

echo "<h3>1. Directory Check:</h3>";
echo "<p>Current directory: " . getcwd() . "</p>";
echo "<p>Files in this directory:</p>";
echo "<ul>";
$files = scandir('.');
foreach ($files as $file) {
    if ($file != '.' && $file != '..') {
        $type = is_dir($file) ? '[DIR]' : '[FILE]';
        $size = is_file($file) ? ' (' . filesize($file) . ' bytes)' : '';
        echo "<li>$type $file$size</li>";
    }
}
echo "</ul>";

echo "<h3>2. Laravel Files Check:</h3>";
$requiredFiles = ['artisan', 'composer.json', 'bootstrap/app.php', 'vendor/autoload.php', '.env'];
foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $file exists</p>";
    } else {
        echo "<p style='color: red;'>❌ $file missing</p>";
    }
}

echo "<h3>3. Web Server Test:</h3>";
echo "<p>Testing if this PHP file is accessible...</p>";
echo "<p style='color: green;'>✅ PHP is working (you can see this page)</p>";

echo "<h3>4. Laravel Bootstrap Test:</h3>";
if (file_exists('vendor/autoload.php') && file_exists('bootstrap/app.php')) {
    try {
        require_once 'vendor/autoload.php';
        $app = require_once 'bootstrap/app.php';
        echo "<p style='color: green;'>✅ Laravel can be bootstrapped</p>";
        
        // Test if we can get routes
        $kernel = $app->make(\Illuminate\Contracts\Http\Kernel::class);
        echo "<p style='color: green;'>✅ Laravel kernel created</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Laravel bootstrap failed: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Laravel files missing</p>";
}

echo "<h3>5. Environment Check:</h3>";
if (file_exists('.env')) {
    echo "<p style='color: green;'>✅ .env file exists</p>";
    $env = file_get_contents('.env');
    if (strpos($env, 'APP_KEY=') !== false) {
        echo "<p style='color: green;'>✅ APP_KEY is set</p>";
    } else {
        echo "<p style='color: red;'>❌ APP_KEY not set</p>";
    }
} else {
    echo "<p style='color: red;'>❌ .env file missing</p>";
}

echo "<h3>6. URL Rewriting Test:</h3>";
if (file_exists('.htaccess')) {
    echo "<p style='color: green;'>✅ .htaccess exists</p>";
    echo "<p>Content preview:</p>";
    echo "<pre>" . htmlspecialchars(substr(file_get_contents('.htaccess'), 0, 500)) . "</pre>";
} else {
    echo "<p style='color: red;'>❌ .htaccess missing</p>";
}

if (file_exists('index.php')) {
    echo "<p style='color: green;'>✅ index.php exists</p>";
    echo "<p>Size: " . filesize('index.php') . " bytes</p>";
} else {
    echo "<p style='color: red;'>❌ index.php missing</p>";
}

echo "<h3>7. Direct API Test:</h3>";
echo "<p>Try these direct links:</p>";
echo "<ul>";
echo "<li><a href='https://ebisera.com/api/' target='_blank'>https://ebisera.com/api/</a></li>";
echo "<li><a href='https://ebisera.com/api/index.php' target='_blank'>https://ebisera.com/api/index.php</a></li>";
echo "</ul>";

echo "<hr>";
echo "<p><strong>⚠️ Delete this file after testing!</strong></p>";
?>
