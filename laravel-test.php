<?php
// Laravel Configuration Test
// Upload this to your public_html/api/ folder
// URL: https://yourdomain.com/api/laravel-test.php

echo "<h2>🔍 Laravel Configuration Test</h2>";

// Check if <PERSON><PERSON> is properly set up
if (file_exists('artisan')) {
    echo "<p style='color: green;'>✅ Laravel detected (artisan file found)</p>";
    
    // Check if vendor directory exists
    if (is_dir('vendor')) {
        echo "<p style='color: green;'>✅ Vendor directory exists</p>";
    } else {
        echo "<p style='color: red;'>❌ Vendor directory missing - run 'composer install'</p>";
    }
    
    // Check if bootstrap/cache is writable
    if (is_writable('bootstrap/cache')) {
        echo "<p style='color: green;'>✅ Bootstrap cache is writable</p>";
    } else {
        echo "<p style='color: red;'>❌ Bootstrap cache is not writable - check permissions</p>";
    }
    
    // Check if storage is writable
    if (is_writable('storage')) {
        echo "<p style='color: green;'>✅ Storage directory is writable</p>";
    } else {
        echo "<p style='color: red;'>❌ Storage directory is not writable - check permissions</p>";
    }
    
    // Try to run Laravel commands
    echo "<h3>🔧 Running Laravel Commands:</h3>";
    
    // Check config cache
    echo "<h4>Config Cache:</h4>";
    exec('php artisan config:cache 2>&1', $output1, $return1);
    if ($return1 === 0) {
        echo "<p style='color: green;'>✅ Config cached successfully</p>";
    } else {
        echo "<p style='color: red;'>❌ Config cache failed:</p>";
        echo "<pre>" . implode("\n", $output1) . "</pre>";
    }
    
    // Check route cache
    echo "<h4>Route Cache:</h4>";
    exec('php artisan route:cache 2>&1', $output2, $return2);
    if ($return2 === 0) {
        echo "<p style='color: green;'>✅ Routes cached successfully</p>";
    } else {
        echo "<p style='color: red;'>❌ Route cache failed:</p>";
        echo "<pre>" . implode("\n", $output2) . "</pre>";
    }
    
    // Test database migration status
    echo "<h4>Migration Status:</h4>";
    exec('php artisan migrate:status 2>&1', $output3, $return3);
    if ($return3 === 0) {
        echo "<p style='color: green;'>✅ Migration status:</p>";
        echo "<pre>" . implode("\n", $output3) . "</pre>";
    } else {
        echo "<p style='color: red;'>❌ Migration status failed:</p>";
        echo "<pre>" . implode("\n", $output3) . "</pre>";
        
        echo "<h4>🔧 Try running migrations:</h4>";
        exec('php artisan migrate --force 2>&1', $output4, $return4);
        echo "<pre>" . implode("\n", $output4) . "</pre>";
    }
    
} else {
    echo "<p style='color: red;'>❌ Laravel not found (artisan file missing)</p>";
    echo "<p>Make sure you've uploaded the Laravel files to the correct directory.</p>";
}

// Check PHP version and extensions
echo "<h3>🐘 PHP Information:</h3>";
echo "<p>PHP Version: " . phpversion() . "</p>";

$required_extensions = ['pdo', 'pdo_mysql', 'mbstring', 'openssl', 'tokenizer', 'xml', 'ctype', 'json'];
echo "<h4>Required Extensions:</h4>";
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p style='color: green;'>✅ $ext</p>";
    } else {
        echo "<p style='color: red;'>❌ $ext (missing)</p>";
    }
}

echo "<hr>";
echo "<p><strong>⚠️ IMPORTANT: Delete this file after testing for security!</strong></p>";
?>
