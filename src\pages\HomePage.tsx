import React from 'react';
import { Link } from 'react-router-dom';
import { Car, Users, Shield, Search, MapPin, UserRound } from 'lucide-react';
import MainLayout from '../components/layout/MainLayout';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import { useCars } from '../context/CarContext';
import { useDrivers } from '../context/DriverContext';
import CarCard from '../components/features/CarCard';
import DriverCard from '../components/features/DriverCard';

const HomePage: React.FC = () => {
  const { activeCars } = useCars(); // Only show active cars on homepage
  const { filteredDrivers } = useDrivers();
  const featuredCars = activeCars.slice(0, 3);
  const featuredDrivers = filteredDrivers.slice(0, 3);

  return (
    <MainLayout>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-primary-900 to-primary-700 text-white">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute inset-0 bg-black bg-opacity-50"></div>
          <div
            className="absolute inset-0 opacity-20"
            style={{
              backgroundImage: "url('https://images.pexels.com/photos/170811/pexels-photo-170811.jpeg')",
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          ></div>
        </div>

        <div className="container mx-auto px-4 py-16 md:py-24 relative z-10">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-4 leading-tight">
              Rent Cars Directly From Local Owners
            </h1>
            <p className="text-xl mb-8 text-gray-100">
              Find affordable rentals from car owners in your area.
              No middlemen, no hidden fees — just direct peer-to-peer car rentals.
            </p>
            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
              <Link to="/cars">
                <Button size="lg" className="w-full sm:w-auto">
                  Browse Available Cars
                </Button>
              </Link>
              <Link to="/signup">
                <Button variant="outline" size="lg" className="w-full sm:w-auto bg-white bg-opacity-10 hover:bg-opacity-20 border-white text-white hover:text-gray-900">
                  List Your Car
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-3">How It Works</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Park & Rent connects car owners with people who need short-term rentals
              in a simple, transparent way.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* For Renters */}
            <div className="text-center p-6 rounded-lg">
              <Search className="w-12 h-12 text-primary-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-3">Find a Car</h3>
              <p className="text-gray-600">
                Browse our selection of available cars in your area.
                Filter by location, price, and features to find your perfect match.
              </p>
            </div>

            {/* Contact */}
            <div className="text-center p-6 rounded-lg">
              <Users className="w-12 h-12 text-primary-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-3">Connect Directly</h3>
              <p className="text-gray-600">
                Once verified, contact car owners directly through our platform.
                Arrange pickup times and locations that work for both of you.
              </p>
            </div>

            {/* Pay */}
            <div className="text-center p-6 rounded-lg">
              <Shield className="w-12 h-12 text-primary-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-3">Safe & Simple</h3>
              <p className="text-gray-600">
                Handle payments directly with the owner. We verify drivers and
                owners to ensure a safe and trustworthy experience.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Cars Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-bold">Featured Cars</h2>
            <Link to="/cars">
              <Button variant="outline">
                View All Cars
              </Button>
            </Link>
          </div>

          {featuredCars.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredCars.map((car) => (
                <CarCard key={car.id} car={car} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200">
              <Car size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-xl font-medium text-gray-900 mb-2">No cars available right now</h3>
              <p className="text-gray-600 mb-6">
                Check back soon or be the first to list your car!
              </p>
              <Link to="/signup">
                <Button variant="primary">
                  List Your Car
                </Button>
              </Link>
            </div>
          )}
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-secondary-700 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Car className="h-16 w-16 mx-auto mb-6 text-white opacity-80" />
            <h2 className="text-3xl font-bold mb-4">Have a Car Sitting Idle?</h2>
            <p className="text-xl mb-8">
              Turn your parked car into extra income. List your vehicle and
              connect with people who need short-term rentals in your area.
            </p>
            <Link to="/signup">
              <Button size="lg" className="bg-white text-gray-900 hover:bg-gray-100 hover:text-gray-800 font-semibold">
                Start Listing Today
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Location Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-3">Available Across Rwanda</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Find cars in these popular locations and many more across Rwanda.
            </p>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {['Kigali', 'Butare', 'Gisenyi', 'Ruhengeri', 'Cyangugu', 'Kibuye', 'Byumba', 'Kibungo', 'Gitarama', 'Nyanza'].map((city) => (
              <div key={city} className="p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-center">
                <MapPin className="h-5 w-5 text-primary-600 mx-auto mb-2" />
                <span className="font-medium">{city}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Drivers Section */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Featured Drivers</h2>
              <p className="text-gray-600">Professional drivers ready to help you get around</p>
            </div>

            <Link to="/drivers">
              <Button variant="outline">
                View All Drivers
              </Button>
            </Link>
          </div>

          {featuredDrivers.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredDrivers.map((driver) => (
                <DriverCard key={driver.id} driver={driver} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200">
              <UserRound size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-xl font-medium text-gray-900 mb-2">No drivers available right now</h3>
              <p className="text-gray-600 mb-6">
                Check back soon or register as a driver yourself!
              </p>
              <Link to="/driver/register">
                <Button variant="primary">
                  Register as Driver
                </Button>
              </Link>
            </div>
          )}
        </div>
      </section>
    </MainLayout>
  );
};

export default HomePage;