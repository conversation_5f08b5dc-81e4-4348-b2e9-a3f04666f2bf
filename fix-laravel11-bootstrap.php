<?php
// Fix Laravel 11 Bootstrap
// Upload this to your public_html/api/ folder
// URL: https://ebisera.com/api/fix-laravel11-bootstrap.php

echo "<h2>🔧 Fix Laravel 11 Bootstrap</h2>";

if (!file_exists('artisan')) {
    die("<p style='color: red;'>❌ Not in Laravel directory</p>");
}

echo "<h3>1. Creating correct Laravel 11 index.php...</h3>";

// Laravel 11 compatible index.php with CORS
$indexContent = '<?php

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

define(\'LARAVEL_START\', microtime(true));

// Add CORS headers early
header(\'Access-Control-Allow-Origin: *\');
header(\'Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS\');
header(\'Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept\');

// Handle preflight OPTIONS requests
if ($_SERVER[\'REQUEST_METHOD\'] === \'OPTIONS\') {
    http_response_code(200);
    exit();
}

// Determine if the application is in maintenance mode...
if (file_exists($maintenance = __DIR__.\'/storage/framework/maintenance.php\')) {
    require $maintenance;
}

// Register the Composer autoloader...
require __DIR__.\'/vendor/autoload.php\';

// Bootstrap Laravel and handle the request...
/** @var Application $app */
$app = require_once __DIR__.\'/bootstrap/app.php\';

$app->handleRequest(Request::capture());
';

if (file_put_contents('index.php', $indexContent)) {
    echo "<p style='color: green;'>✅ Created Laravel 11 compatible index.php</p>";
} else {
    echo "<p style='color: red;'>❌ Failed to create index.php</p>";
}

echo "<h3>2. Creating optimized .htaccess...</h3>";

$htaccessContent = '<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Handle preflight OPTIONS requests
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

# CORS Headers
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Accept, Origin"
    Header always set Access-Control-Max-Age "3600"
</IfModule>

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Disable directory browsing
Options -Indexes

# Protect sensitive files
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.json">
    Order allow,deny
    Deny from all
</Files>';

if (file_put_contents('.htaccess', $htaccessContent)) {
    echo "<p style='color: green;'>✅ Created optimized .htaccess</p>";
} else {
    echo "<p style='color: red;'>❌ Failed to create .htaccess</p>";
}

echo "<h3>3. Setting proper permissions...</h3>";
exec('chmod -R 755 storage 2>&1');
exec('chmod -R 755 bootstrap/cache 2>&1');
if (file_exists('.env')) {
    chmod('.env', 0644);
}
echo "<p style='color: green;'>✅ Permissions set</p>";

echo "<h3>4. Clearing all caches...</h3>";
exec('php artisan config:clear 2>&1');
exec('php artisan route:clear 2>&1');
exec('php artisan cache:clear 2>&1');
exec('php artisan view:clear 2>&1');
echo "<p style='color: green;'>✅ All caches cleared</p>";

echo "<h3>5. Generating app key...</h3>";
exec('php artisan key:generate --force 2>&1');
echo "<p style='color: green;'>✅ App key generated</p>";

echo "<h3>6. Running migrations...</h3>";
exec('php artisan migrate --force 2>&1', $migrateOutput, $migrateReturn);
if ($migrateReturn === 0) {
    echo "<p style='color: green;'>✅ Migrations completed</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Migration output:</p>";
    echo "<pre>" . implode("\n", array_slice($migrateOutput, -10)) . "</pre>";
}

echo "<h3>7. Seeding database...</h3>";
exec('php artisan db:seed --force 2>&1', $seedOutput, $seedReturn);
if ($seedReturn === 0) {
    echo "<p style='color: green;'>✅ Database seeded</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Seeding output:</p>";
    echo "<pre>" . implode("\n", array_slice($seedOutput, -5)) . "</pre>";
}

echo "<h3>8. Creating storage link...</h3>";
exec('php artisan storage:link 2>&1');
echo "<p style='color: green;'>✅ Storage link created</p>";

echo "<h3>9. Optimizing for production...</h3>";
exec('php artisan config:cache 2>&1');
exec('php artisan route:cache 2>&1');
echo "<p style='color: green;'>✅ Production optimization complete</p>";

echo "<h3>10. Testing Laravel bootstrap...</h3>";
try {
    // Test the new bootstrap
    $testUrl = 'https://ebisera.com/api/';
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'ignore_errors' => true
        ]
    ]);
    
    $response = @file_get_contents($testUrl, false, $context);
    if ($response !== false) {
        echo "<p style='color: green;'>✅ Laravel is responding</p>";
    } else {
        echo "<p style='color: red;'>❌ Laravel not responding yet</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Test error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>✅ Laravel 11 Bootstrap Fixed!</h3>";

echo "<h4>🧪 Test these URLs now:</h4>";
echo "<ul>";
echo "<li><a href='https://ebisera.com/api/' target='_blank'>API Root</a> (should work)</li>";
echo "<li><a href='https://ebisera.com/api/cars' target='_blank'>Cars API</a> (should return JSON)</li>";
echo "<li><a href='https://ebisera.com/api/drivers' target='_blank'>Drivers API</a> (should return JSON)</li>";
echo "</ul>";

echo "<h4>🎯 What was fixed:</h4>";
echo "<ul>";
echo "<li>✅ Laravel 11 compatible bootstrap process</li>";
echo "<li>✅ Proper service provider loading</li>";
echo "<li>✅ CORS headers for API access</li>";
echo "<li>✅ Database migrations and seeding</li>";
echo "<li>✅ Production optimization</li>";
echo "</ul>";

echo "<p><strong>⚠️ Delete this file after testing!</strong></p>";
?>
