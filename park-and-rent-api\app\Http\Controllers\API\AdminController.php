<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Car;
use App\Models\Driver;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class AdminController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        // Middleware is handled in routes/api.php
    }

    /**
     * Get dashboard statistics.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function dashboard()
    {
        // Check if user is admin
        if (!auth()->check() || auth()->user()->role !== 'admin') {
            return response()->json(['message' => 'Unauthorized. Admin access required.'], 403);
        }

        try {
            $stats = [
                'users_count' => User::count(),
                'cars_count' => Car::count(),
                'drivers_count' => Driver::count(),
                'bookings_count' => Booking::count(),
                'pending_bookings' => Booking::where('status', 'pending')->count(),
                'completed_bookings' => Booking::where('status', 'completed')->count(),
                'cancelled_bookings' => Booking::where('status', 'cancelled')->count(),
                'pending_driver_verifications' => Driver::where('license_verification_status', 'pending')->count(),
                'revenue' => Booking::where('status', 'completed')->sum('total_price') ?? 0,
                'recent_bookings' => Booking::with(['user'])->orderBy('created_at', 'desc')->limit(5)->get(),
                'recent_users' => User::orderBy('created_at', 'desc')->limit(5)->get(),
            ];

            return response()->json($stats);
        } catch (\Exception $e) {
            Log::error('Admin dashboard error: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to fetch dashboard data',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }



    /**
     * Get all users.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function users()
    {
        $users = User::all();
        return response()->json($users);
    }

    /**
     * Get all cars.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function cars()
    {
        $cars = Car::with('owner')->get();
        return response()->json($cars);
    }

    /**
     * Get all drivers.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function drivers()
    {
        $drivers = Driver::with('user')->get();
        return response()->json($drivers);
    }

    /**
     * Get all bookings.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function bookings()
    {
        $bookings = Booking::with(['user'])->get();
        return response()->json($bookings);
    }

    /**
     * Verify a driver's license.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyDriverLicense(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:pending,verified,rejected',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $driver = Driver::findOrFail($id);
        $driver->update([
            'license_verification_status' => $request->status
        ]);

        // Also update the user's license verification status
        $user = User::find($driver->user_id);
        $user->update([
            'license_verification_status' => $request->status
        ]);

        return response()->json([
            'message' => 'Driver license verification status updated successfully',
            'status' => $request->status
        ]);
    }

    /**
     * Update user role.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateUserRole(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'role' => 'required|in:client,owner,driver,admin',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = User::findOrFail($id);
        $user->update([
            'role' => $request->role
        ]);

        return response()->json([
            'message' => 'User role updated successfully',
            'role' => $request->role
        ]);
    }

    /**
     * Get revenue statistics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function revenue(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'period' => 'nullable|in:daily,weekly,monthly,yearly',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $query = Booking::where('status', 'completed');

        // Apply date filters if provided
        if ($request->has('start_date')) {
            $query->where('created_at', '>=', $request->start_date);
        }

        if ($request->has('end_date')) {
            $query->where('created_at', '<=', $request->end_date);
        }

        // Group by period if specified
        if ($request->has('period')) {
            $period = $request->period;

            if ($period === 'daily') {
                $stats = $query->select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('SUM(total_price) as revenue'),
                    DB::raw('COUNT(*) as count')
                )
                ->groupBy('date')
                ->orderBy('date')
                ->get();
            } elseif ($period === 'weekly') {
                $stats = $query->select(
                    DB::raw('YEAR(created_at) as year'),
                    DB::raw('WEEK(created_at) as week'),
                    DB::raw('SUM(total_price) as revenue'),
                    DB::raw('COUNT(*) as count')
                )
                ->groupBy('year', 'week')
                ->orderBy('year')
                ->orderBy('week')
                ->get();
            } elseif ($period === 'monthly') {
                $stats = $query->select(
                    DB::raw('YEAR(created_at) as year'),
                    DB::raw('MONTH(created_at) as month'),
                    DB::raw('SUM(total_price) as revenue'),
                    DB::raw('COUNT(*) as count')
                )
                ->groupBy('year', 'month')
                ->orderBy('year')
                ->orderBy('month')
                ->get();
            } elseif ($period === 'yearly') {
                $stats = $query->select(
                    DB::raw('YEAR(created_at) as year'),
                    DB::raw('SUM(total_price) as revenue'),
                    DB::raw('COUNT(*) as count')
                )
                ->groupBy('year')
                ->orderBy('year')
                ->get();
            }

            return response()->json($stats);
        }

        // If no period specified, return total
        $total = [
            'revenue' => $query->sum('total_price'),
            'count' => $query->count(),
        ];

        return response()->json($total);
    }
}
