<?php
// Fix API Now - Create Missing Files
// Upload this to your public_html/api/ folder and run it
// URL: https://ebisera.com/api/fix-api-now.php

echo "<h2>🚀 Fix API - Create Missing Files</h2>";

// 1. Create index.php
echo "<h3>1. Creating index.php...</h3>";
$indexContent = '<?php

use Illuminate\Contracts\Http\Kernel;
use Illuminate\Http\Request;

define(\'LARAVEL_START\', microtime(true));

if (file_exists($maintenance = __DIR__.\'/storage/framework/maintenance.php\')) {
    require $maintenance;
}

require __DIR__.\'/vendor/autoload.php\';

$app = require_once __DIR__.\'/bootstrap/app.php\';

$kernel = $app->make(Kernel::class);

$response = $kernel->handle(
    $request = Request::capture()
)->send();

$kernel->terminate($request, $response);
';

file_put_contents('index.php', $indexContent);
echo "<p style='color: green;'>✅ index.php created</p>";

// 2. Create .htaccess
echo "<h3>2. Creating .htaccess...</h3>";
$htaccessContent = '<IfModule mod_rewrite.c>
    RewriteEngine On
    
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
    
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
</IfModule>

Options -Indexes';

file_put_contents('.htaccess', $htaccessContent);
echo "<p style='color: green;'>✅ .htaccess created</p>";

// 3. Set permissions and run Laravel commands
echo "<h3>3. Setting up Laravel...</h3>";
exec('chmod -R 755 storage');
exec('chmod -R 755 bootstrap/cache');
exec('php artisan config:clear');
exec('php artisan route:clear');
exec('php artisan cache:clear');
exec('php artisan key:generate --force');
exec('php artisan migrate --force');
exec('php artisan db:seed --force');
exec('php artisan storage:link');
exec('php artisan config:cache');
exec('php artisan route:cache');

echo "<p style='color: green;'>✅ Laravel setup complete</p>";

echo "<hr>";
echo "<h3>✅ API Fixed!</h3>";
echo "<p>Test these URLs:</p>";
echo "<ul>";
echo "<li><a href='https://ebisera.com/api/cars' target='_blank'>Cars API</a></li>";
echo "<li><a href='https://ebisera.com/api/drivers' target='_blank'>Drivers API</a></li>";
echo "</ul>";
echo "<p><strong>Delete this file after testing!</strong></p>";
?>
