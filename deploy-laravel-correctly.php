<?php
// Deploy <PERSON>vel Correctly for Hostinger
// Upload this to your public_html/api/ folder (where your Laravel files are)
// URL: https://ebisera.com/api/deploy-laravel-correctly.php

echo "<h2>🚀 Deploy Laravel Correctly</h2>";

$currentDir = getcwd();
echo "<p>Working in: $currentDir</p>";

// Check if we're in the Laravel directory
if (!file_exists('artisan')) {
    die("<p style='color: red;'>❌ Not in Laravel directory. Make sure this script is in your api folder with Laravel files.</p>");
}

echo "<h3>📁 Current Laravel Structure:</h3>";
$files = scandir('.');
echo "<ul>";
foreach ($files as $file) {
    if ($file != '.' && $file != '..') {
        $type = is_dir($file) ? '[DIR]' : '[FILE]';
        echo "<li>$type $file</li>";
    }
}
echo "</ul>";

// 1. Copy <PERSON>'s public/index.php to api root
echo "<h3>1. 🔧 Setting up <PERSON><PERSON> entry point...</h3>";

if (file_exists('public/index.php')) {
    $publicIndexContent = file_get_contents('public/index.php');
    
    // Update the paths to work from api root instead of public folder
    $apiIndexContent = str_replace(
        "require __DIR__.'/../vendor/autoload.php';",
        "require __DIR__.'/vendor/autoload.php';",
        $publicIndexContent
    );
    
    $apiIndexContent = str_replace(
        "require_once __DIR__.'/../bootstrap/app.php';",
        "require_once __DIR__.'/bootstrap/app.php';",
        $apiIndexContent
    );
    
    $apiIndexContent = str_replace(
        "file_exists(\$maintenance = __DIR__.'/../storage/framework/maintenance.php')",
        "file_exists(\$maintenance = __DIR__.'/storage/framework/maintenance.php')",
        $apiIndexContent
    );
    
    // Add CORS headers at the top
    $corsHeaders = "<?php

// Add CORS headers for API
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept');

// Handle preflight OPTIONS requests
if (\$_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

";
    
    $apiIndexContent = str_replace('<?php', $corsHeaders, $apiIndexContent);
    
    if (file_put_contents('index.php', $apiIndexContent)) {
        echo "<p style='color: green;'>✅ Created index.php in API root with correct paths</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create index.php</p>";
    }
} else {
    echo "<p style='color: red;'>❌ public/index.php not found</p>";
}

// 2. Copy .htaccess from public to api root
echo "<h3>2. 🔧 Setting up URL rewriting...</h3>";

$htaccessContent = '<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Handle preflight OPTIONS requests
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

# CORS Headers
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Accept, Origin"
    Header always set Access-Control-Max-Age "3600"
</IfModule>

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Disable directory browsing
Options -Indexes

# Protect sensitive files
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.json">
    Order allow,deny
    Deny from all
</Files>';

if (file_put_contents('.htaccess', $htaccessContent)) {
    echo "<p style='color: green;'>✅ Created .htaccess with proper rewriting rules</p>";
} else {
    echo "<p style='color: red;'>❌ Failed to create .htaccess</p>";
}

// 3. Set proper permissions
echo "<h3>3. 🔒 Setting permissions...</h3>";
exec('chmod -R 755 storage 2>&1');
exec('chmod -R 755 bootstrap/cache 2>&1');
if (file_exists('.env')) {
    chmod('.env', 0644);
}
echo "<p style='color: green;'>✅ Permissions set</p>";

// 4. Clear Laravel caches
echo "<h3>4. 🧹 Clearing Laravel caches...</h3>";
exec('php artisan config:clear 2>&1');
exec('php artisan route:clear 2>&1');
exec('php artisan view:clear 2>&1');
exec('php artisan cache:clear 2>&1');
echo "<p style='color: green;'>✅ Caches cleared</p>";

// 5. Generate app key if needed
echo "<h3>5. 🔑 Checking app key...</h3>";
if (file_exists('.env')) {
    $env = file_get_contents('.env');
    if (strpos($env, 'APP_KEY=') === false || strpos($env, 'APP_KEY=base64:') === false) {
        exec('php artisan key:generate --force 2>&1');
        echo "<p style='color: green;'>✅ App key generated</p>";
    } else {
        echo "<p style='color: green;'>✅ App key already exists</p>";
    }
} else {
    echo "<p style='color: red;'>❌ .env file not found</p>";
}

// 6. Run migrations
echo "<h3>6. 🗄️ Setting up database...</h3>";
exec('php artisan migrate --force 2>&1', $migrateOutput, $migrateReturn);
if ($migrateReturn === 0) {
    echo "<p style='color: green;'>✅ Migrations completed</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Migration output:</p>";
    echo "<pre>" . implode("\n", array_slice($migrateOutput, -10)) . "</pre>";
}

// 7. Seed database
exec('php artisan db:seed --force 2>&1', $seedOutput, $seedReturn);
if ($seedReturn === 0) {
    echo "<p style='color: green;'>✅ Database seeded</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Seeding output:</p>";
    echo "<pre>" . implode("\n", array_slice($seedOutput, -5)) . "</pre>";
}

// 8. Create storage link
exec('php artisan storage:link 2>&1');
echo "<p style='color: green;'>✅ Storage link created</p>";

// 9. Cache for production
echo "<h3>7. ⚡ Optimizing for production...</h3>";
exec('php artisan config:cache 2>&1');
exec('php artisan route:cache 2>&1');
echo "<p style='color: green;'>✅ Production optimization complete</p>";

// 10. Test Laravel
echo "<h3>8. 🧪 Testing Laravel setup...</h3>";
try {
    if (file_exists('vendor/autoload.php') && file_exists('bootstrap/app.php')) {
        require_once 'vendor/autoload.php';
        $app = require_once 'bootstrap/app.php';
        echo "<p style='color: green;'>✅ Laravel application loads successfully</p>";
    } else {
        echo "<p style='color: red;'>❌ Laravel files missing</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Laravel error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>✅ Laravel Deployment Complete!</h3>";

echo "<h4>📋 Files created/updated:</h4>";
echo "<ul>";
if (file_exists('index.php')) echo "<li style='color: green;'>✅ index.php (Laravel entry point)</li>";
if (file_exists('.htaccess')) echo "<li style='color: green;'>✅ .htaccess (URL rewriting)</li>";
echo "</ul>";

echo "<h4>🧪 Test these URLs now:</h4>";
echo "<ul>";
echo "<li><a href='https://ebisera.com/api/' target='_blank'>https://ebisera.com/api/</a> (should show Laravel info)</li>";
echo "<li><a href='https://ebisera.com/api/cars' target='_blank'>https://ebisera.com/api/cars</a> (should return JSON)</li>";
echo "<li><a href='https://ebisera.com/api/drivers' target='_blank'>https://ebisera.com/api/drivers</a> (should return JSON)</li>";
echo "</ul>";

echo "<h4>🎯 Next steps:</h4>";
echo "<ol>";
echo "<li>Test the API URLs above</li>";
echo "<li>Check your frontend at <a href='https://ebisera.com' target='_blank'>https://ebisera.com</a></li>";
echo "<li>The 404 errors should be gone!</li>";
echo "<li>Delete this file for security</li>";
echo "</ol>";

echo "<p><strong>⚠️ IMPORTANT: Delete this file after testing!</strong></p>";
?>
